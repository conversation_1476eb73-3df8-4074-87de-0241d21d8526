# src/services/notification_service.py
import json
import logging
from typing import Dict, Any

# redis_client 模块的路径需要确认，这里暂时使用假设的路径
# 我们需要确保能够从这里正确导入RedisClient
from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)

class NotificationService:
    """
    负责将业务事件发布到Redis的Pub/Sub频道。
    这是新事件驱动架构的核心组件。
    """
    def __init__(self, redis_client: RedisClient):
        """
        通过依赖注入初始化，复用现有的Redis客户端。

        Args:
            redis_client: 项目中已配置好的RedisClient实例。
        """
        self.redis_client = redis_client

    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        """
        内部方法，用于将事件发布到指定的频道。

        Args:
            task_id: 任务的唯一标识符，用于构成频道名称。
            event_data: 要发布的事件内容（字典格式）。
        """
        channel = f"task:{task_id}"
        # 使用ensure_ascii=False以支持中文等非ASCII字符
        message = json.dumps(event_data, ensure_ascii=False)
        try:
            # 确保Redis连接
            await self.redis_client._ensure_connected()
            # 使用RedisClient的内部_redis属性进行发布
            await self.redis_client._redis.publish(channel, message)
            logger.debug(f"Published to {channel}: {message}")
        except Exception as e:
            logger.error(f"Failed to publish to Redis channel {channel}: {e}")

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        """
        通知一个步骤已经开始。

        Args:
            task_id: 任务ID。
            step_name: 步骤的内部名称 (e.g., "core_intent_analysis")。
            title: 前端展示的步骤标题 (e.g., "解析用户需求")。
            message: 当前状态的描述信息。
        """
        event = {
            "event": "step_start",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "title": title,
                "message": message
            }
        }
        await self._publish(task_id, event)

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        """
        通知一个步骤已经结束。

        Args:
            task_id: 任务ID。
            step_name: 步骤的内部名称。
            status: 结束状态 ('success' or 'error')。
            result: 步骤的产出结果，将传递给前端。
        """
        event = {
            "event": "step_end",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "status": status,
                "result": result or {}
            }
        }
        await self._publish(task_id, event)

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        """
        通知最终的完整结果已经产生。

        Args:
            task_id: 任务ID。
            final_data: 完整的最终产出数据。
        """
        event = {
            "event": "complete",
            "data": final_data
        }
        await self._publish(task_id, event)
        # 紧接着发送一个流结束信号，以便前端可以安全地关闭连接
        await self._publish(task_id, {"event": "eos"}) # End of Stream

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        """
        通知一个严重错误发生，该错误将中断整个流程。

        Args:
            task_id: 任务ID。
            error_message: 错误的描述信息。
            step_name: 发生错误的步骤名称。
        """
        event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message
            }
        }
        await self._publish(task_id, event)
        # 发生错误后，同样发送流结束信号
        await self._publish(task_id, {"event": "eos"}) 