"""
TravelPlannerAgent LangGraph实现

基于LangGraph重构的旅行规划Agent，替换原有的autogen实现。
专注于全自动模式实现，支持：
- 完全自动化的旅行规划流程
- 双模运行（精准续航规划 vs 通用驾驶辅助）
- 状态管理和SSE事件流
- 交互模式预留钩子
"""

import logging
import time
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
from datetime import datetime

from .travel_planner_langgraph import TravelPlannerGraph, create_initial_state, UserProfile, VehicleInfo
from .services import UserProfileService, MemoryService

# 导入新的NotificationService
from src.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class TravelPlannerAgentLangGraph:
    """
    基于LangGraph的旅行规划Agent

    专注于全自动模式实现，提供完整的旅行规划服务：
    - 全自动规划：无需用户交互，直接生成完整方案
    - 流式规划：实时进度更新，适合前端展示
    - 双模运行：支持精准续航规划和通用驾驶辅助
    - 交互预留：为未来交互模式预留接口
    """

    def __init__(self, enable_interaction_hooks: bool = False):
        """
        初始化Agent

        Args:
            enable_interaction_hooks: 是否启用交互模式钩子（预留功能）
        """
        self.graph = TravelPlannerGraph(enable_interaction_hooks=enable_interaction_hooks)
        self.user_profile_service = UserProfileService()
        self.memory_service = MemoryService()
        self.enable_interaction_hooks = enable_interaction_hooks
    
    # ==================== V2版新方法 ====================

    async def run_planning_with_notifications(
        self,
        task_id: str,
        user_id: str,
        query: str,
        notification_service: NotificationService,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None
    ):
        """
        [V2] 运行规划并将事件发布到Redis。
        此方法在后台任务中执行，不直接返回结果给API。

        Args:
            task_id: 唯一的任务ID (等同于session_id)。
            user_id: 用户ID。
            query: 用户的原始查询。
            notification_service: 用于发布事件的通知服务实例。
            user_profile: 用户画像（可选）。
            vehicle_info: 车辆信息（可选）。
        """
        try:
            logger.info(f"[V2] 开始后台规划任务 - Task ID: {task_id}")

            # 如果没有提供用户画像，则从数据库获取
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)

            # 通过config将notification_service注入到LangGraph的每个节点中
            config = {
                "configurable": {
                    "notification_service": notification_service,
                    "session_id": task_id, # 确保节点能访问到session_id
                    "user_id": user_id,
                }
            }

            # 运行工作流，注意这里我们只关心执行，不关心返回值
            # 因为所有状态都通过notification_service发出
            final_state = await self.graph.run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=task_id,
                config=config
            )

            # 虽然主要事件已发出，但可以在此发布最终完成事件或保存记忆
            await notification_service.notify_final_result(task_id, final_state)
            
            # 保存用户记忆
            await self._save_planning_memory(user_id, query, final_state)
            
            logger.info(f"[V2] 后台规划任务完成 - Task ID: {task_id}")

        except Exception as e:
            logger.error(f"[V2] 后台规划任务失败 - Task ID: {task_id}, 错误: {e}")
            # 发生异常时，通过通知服务发布错误事件
            await notification_service.notify_error(task_id, str(e), "agent_execution")
    
    async def plan_travel_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        全自动规划旅行（非流式）

        这是主要的规划方法，完全自动化处理用户请求，
        无需任何用户交互，直接生成完整的旅行方案。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Returns:
            完整的规划结果
        """
        try:
            logger.info(f"开始旅行规划 - 用户: {user_id}")
            
            start_time = datetime.now()
            
            # 获取用户画像（如果未提供）
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)
            
            # 运行全自动工作流
            final_state = await self.graph.run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id
            )
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            final_state["processing_time_seconds"] = processing_time
            
            # 保存用户记忆
            await self._save_planning_memory(user_id, query, final_state)
            
            # 转换为API响应格式 - 注意：这里需要一个新的方法，因为旧的adapter已经没了
            # 暂时返回原始state，或者创建一个新的转换函数
            # response = self.stream_adapter.convert_final_state_to_response(final_state)
            response = final_state
            
            logger.info(f"旅行规划完成 - 用户: {user_id}, 耗时: {processing_time:.2f}秒")
            return response
            
        except Exception as e:
            logger.error(f"旅行规划失败 - 用户: {user_id}, 错误: {str(e)}")
            raise
    
    async def plan_travel_stream_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        全自动流式规划旅行

        提供实时的规划进度更新，完全自动化处理，
        适合前端SSE事件流展示。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Yields:
            SSE格式的事件流
        """
        try:
            logger.info(f"开始流式旅行规划 - 用户: {user_id}")
            
            # 生成会话ID
            if not session_id:
                session_id = f"travel_plan_{user_id}_{int(datetime.now().timestamp())}"
            
            # 获取用户画像（如果未提供）
            if not user_profile:
                user_profile = await self.user_profile_service.get_user_profile(user_id)
            
            # 获取LangGraph全自动流
            langgraph_stream = self.graph.stream_run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id
            )
            
            # 转换为SSE流 - ############### V2重构后此部分逻辑需要废弃 ###############
            # 这里我们暂时返回一个提示，表明此方法已不适用
            yield json.dumps({
                "event": "deprecation_warning", 
                "data": { "message": "This streaming method is deprecated. Please use the /v2/plan/stream API." }
            })
            return

            final_state = None
            # async for sse_event in self.stream_adapter.convert_langgraph_stream_to_sse(
            #     langgraph_stream, session_id
            # ):
            #     yield sse_event
                
            #     # 尝试从事件中提取最终状态（用于保存记忆）
            #     if "planning_completed" in sse_event or "planning_failed" in sse_event:
            #         # 获取最终状态用于保存记忆
            #         try:
            #             state_history = await self.graph.get_state_history(session_id)
            #             if state_history:
            #                 final_state = state_history[0]  # 最新状态
            #         except Exception as e:
            #             logger.warning(f"获取最终状态失败: {str(e)}")
            
            # # 保存用户记忆
            # if final_state:
            #     await self._save_planning_memory(user_id, query, final_state)
            
            logger.info(f"流式旅行规划完成 - 用户: {user_id}")

        except Exception as e:
            logger.error(f"流式旅行规划失败 - 用户: {user_id}, 错误: {str(e)}")

            # 发送错误事件
            # error_event = self.stream_adapter._format_sse_event(
            #     event_type="error",
            #     data={
            #         "session_id": session_id or "unknown",
            #         "user_id": user_id,
            #         "error": str(e),
            #         "timestamp": datetime.now().isoformat()
            #     }
            # )
            # yield error_event
            yield json.dumps({
                "event": "error",
                "data": { "message": str(e) }
            })

    # ==================== 向后兼容方法 ====================

    async def plan_travel(self, *args, **kwargs) -> Dict[str, Any]:
        """规划旅行（默认全自动模式，保持向后兼容）"""
        return await self.plan_travel_automatic(*args, **kwargs)

    async def plan_travel_stream(self, *args, **kwargs) -> AsyncGenerator[str, None]:
        """流式规划旅行（默认全自动模式，保持向后兼容）"""
        async for event in self.plan_travel_stream_automatic(*args, **kwargs):
            yield event

    # ==================== 交互模式预留方法 ====================

    async def plan_travel_interactive(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        交互式规划旅行（预留功能）

        支持在关键决策点与用户交互，收集反馈并调整规划。
        目前为预留接口，可在后续版本中实现。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）
            interaction_config: 交互配置（可选）

        Returns:
            交互式规划结果
        """
        logger.info(f"交互式规划模式（预留功能） - 用户: {user_id}")

        # 目前回退到全自动模式
        logger.warning("交互式模式尚未实现，回退到全自动模式")
        return await self.plan_travel_automatic(
            user_id=user_id,
            query=query,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            session_id=session_id
        )

    async def plan_travel_stream_interactive(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[str, None]:
        """
        交互式流式规划旅行 - 分析阶段

        只执行分析阶段，包括意图分析和偏好分析。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）
            interaction_config: 交互配置（可选）

        Yields:
            分析阶段SSE事件流
        """
        logger.info(f"开始分析阶段 - 用户: {user_id}, 查询: {query}")

        try:
            # 使用LangGraph工作流执行分析阶段
            analysis_stream = self.graph.stream_run_analysis_only(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id or f"analysis_{int(time.time())}"
            )

            # 通过stream_adapter转换为SSE格式
            async for sse_event in self.graph.stream_run_analysis_only(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id or f"analysis_{int(time.time())}"
            ):
                yield sse_event

        except Exception as e:
            logger.error(f"分析阶段执行失败: {str(e)}")
            # 发送错误事件
            error_event = {
                "event_type": "error",
                "payload": {
                    "error_message": f"分析失败: {str(e)}",
                    "stage": "analysis"
                }
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

    async def plan_travel_stream_automatic(
        self,
        user_id: str,
        query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        全自动流式规划旅行

        执行完整的旅行规划流程，包括意图分析、POI查询、行程生成等。

        Args:
            user_id: 用户ID
            query: 用户查询
            user_profile: 用户画像（可选）
            vehicle_info: 车辆信息（可选）
            session_id: 会话ID（可选）

        Yields:
            全自动SSE事件流
        """
        logger.info(f"全自动流式规划模式 - 用户: {user_id}")

        try:
            # 使用LangGraph工作流执行全自动规划
            planning_stream = self.graph.stream_run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id or f"auto_{int(time.time())}"
            )

            # 通过stream_adapter转换为SSE格式
            async for sse_event in self.graph.stream_run_automatic(
                user_id=user_id,
                original_query=query,
                user_profile=user_profile,
                vehicle_info=vehicle_info,
                session_id=session_id or f"auto_{int(time.time())}"
            ):
                yield sse_event

        except Exception as e:
            logger.error(f"全自动规划执行失败: {str(e)}")
            # 发送错误事件
            error_event = {
                "event_type": "error",
                "payload": {
                    "error_message": f"规划失败: {str(e)}",
                    "stage": "planning"
                }
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

    async def start_planning_phase(
        self,
        session_id: str,
        analysis_result: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """
        启动规划阶段 - 基于分析结果开始详细规划

        Args:
            session_id: 会话ID
            analysis_result: 分析阶段的结果

        Yields:
            str: SSE格式的事件流
        """
        logger.info(f"启动规划阶段 - Session: {session_id}")
        logger.info(f"分析结果: {analysis_result}")

        try:
            # 使用专门的规划阶段流式方法，传入完整的分析结果
            planning_stream = self.graph.stream_run_planning_phase(
                session_id=session_id,
                user_id=analysis_result.get("user_id", "unknown"),
                original_query=analysis_result.get("query", ""),
                core_intent=analysis_result.get("core_intent"),
                user_profile=analysis_result.get("user_profile"),
                user_memories=analysis_result.get("user_memories"),
                travel_preferences=analysis_result.get("travel_preferences"),
                preference_profile=analysis_result.get("preference_profile"),
                driving_context=analysis_result.get("driving_context"),
                vehicle_info=analysis_result.get("vehicle_info"),
                multi_city_strategy=analysis_result.get("multi_city_strategy")
            )

            # 通过stream_adapter转换为SSE格式
            async for sse_event in self.graph.stream_run_planning_phase(
                session_id=session_id,
                user_id=analysis_result.get("user_id", "unknown"),
                original_query=analysis_result.get("query", ""),
                core_intent=analysis_result.get("core_intent"),
                user_profile=analysis_result.get("user_profile"),
                user_memories=analysis_result.get("user_memories"),
                travel_preferences=analysis_result.get("travel_preferences"),
                preference_profile=analysis_result.get("preference_profile"),
                driving_context=analysis_result.get("driving_context"),
                vehicle_info=analysis_result.get("vehicle_info"),
                multi_city_strategy=analysis_result.get("multi_city_strategy")
            ):
                yield sse_event

        except Exception as e:
            logger.error(f"规划阶段启动失败 - Session: {session_id}, 错误: {str(e)}")
            error_event = {
                "event_type": "error",
                "payload": {
                    "error_message": f"规划启动失败: {str(e)}",
                    "stage": "planning_initiation"
                }
            }
            yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

    async def get_planning_status(self, session_id: str) -> Dict[str, Any]:
        """
        获取规划状态
        
        Args:
            session_id: 会话ID
            
        Returns:
            规划状态信息
        """
        try:
            logger.info(f"获取规划状态 - Session: {session_id}")
            
            # 获取状态历史
            state_history = await self.graph.get_state_history(session_id)
            
            if not state_history:
                return {
                    "session_id": session_id,
                    "status": "not_found",
                    "message": "未找到对应的规划会话"
                }
            
            # 获取最新状态
            latest_state = state_history[0]
            
            # 转换为状态响应
            response = {
                "session_id": session_id,
                "status": "completed" if latest_state.get("is_completed") else "processing",
                "current_stage": latest_state.get("current_stage"),
                "has_error": latest_state.get("has_error", False),
                "error_message": latest_state.get("error_message"),
                "progress": self._calculate_progress(latest_state.get("current_stage")),
                "created_at": latest_state.get("created_at"),
                "updated_at": latest_state.get("updated_at")
            }
            
            # 如果已完成，添加结果
            if latest_state.get("is_completed") and not latest_state.get("has_error"):
                response["result"] = self.graph.convert_final_state_to_response(latest_state)
            
            return response
            
        except Exception as e:
            logger.error(f"获取规划状态失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    async def cancel_planning(self, session_id: str) -> Dict[str, Any]:
        """
        取消规划
        
        Args:
            session_id: 会话ID
            
        Returns:
            取消结果
        """
        try:
            logger.info(f"取消规划 - Session: {session_id}")
            
            # 这里可以实现取消逻辑
            # LangGraph目前没有直接的取消机制，可以通过状态标记实现
            
            return {
                "session_id": session_id,
                "status": "cancelled",
                "message": "规划已取消"
            }
            
        except Exception as e:
            logger.error(f"取消规划失败 - Session: {session_id}, 错误: {str(e)}")
            return {
                "session_id": session_id,
                "status": "error",
                "error": str(e)
            }
    
    def get_graph_visualization(self, include_interaction_hooks: bool = False) -> str:
        """
        获取工作流图可视化

        Args:
            include_interaction_hooks: 是否包含交互模式钩子（预留功能）

        Returns:
            Mermaid格式的图形定义
        """
        return self.graph.get_graph_visualization(include_interaction_hooks=include_interaction_hooks)

    def get_automatic_mode_info(self) -> Dict[str, Any]:
        """
        获取全自动模式信息

        Returns:
            全自动模式的详细信息
        """
        return {
            "mode": "automatic",
            "description": "完全自动化的旅行规划模式",
            "features": [
                "无需用户交互",
                "完整的规划流程",
                "双模运行支持（精准续航规划 vs 通用驾驶辅助）",
                "实时进度更新",
                "状态管理和恢复",
                "SSE事件流支持"
            ],
            "workflow_stages": [
                "核心意图分析",
                "多城市策略分析（如适用）",
                "驾驶情境分析（如适用）",
                "偏好分析",
                "行程生成",
                "行程优化"
            ],
            "interaction_hooks_enabled": self.enable_interaction_hooks,
            "interaction_hooks_available": [
                "用户确认节点（预留）",
                "用户反馈节点（预留）",
                "交互式规划方法（预留）",
                "交互式流式规划方法（预留）"
            ]
        }
    
    async def _save_planning_memory(
        self,
        user_id: str,
        query: str,
        final_state: Dict[str, Any]
    ):
        """保存规划记忆"""
        try:
            memory_content = {
                "original_query": query,
                "destinations": final_state.get("core_intent", {}).get("destinations", []),
                "days": final_state.get("core_intent", {}).get("days"),
                "planning_mode": final_state.get("planning_mode"),
                "success": not final_state.get("has_error", False),
                "session_id": final_state.get("session_id")
            }
            
            metadata = {
                "processing_time": final_state.get("processing_time_seconds", 0),
                "tokens_used": final_state.get("tokens_used", 0),
                "cost_estimate": final_state.get("cost_estimate", 0)
            }
            
            await self.memory_service.save_memory(
                user_id=user_id,
                memory_type="travel_planning",
                content=memory_content,
                metadata=metadata,
                importance=8 if not final_state.get("has_error") else 3,
                tags=["travel_planning", "langgraph"]
            )
            
            logger.info(f"规划记忆保存成功 - 用户: {user_id}")
            
        except Exception as e:
            logger.warning(f"保存规划记忆失败 - 用户: {user_id}, 错误: {str(e)}")
    
    def _calculate_progress(self, current_stage: Optional[str]) -> int:
        """根据当前阶段计算进度百分比"""
        stage_progress = {
            "intent_analysis": 20,
            "multi_city_strategy": 40,
            "driving_context": 50,
            "preference_analysis": 70,
            "itinerary_generation": 90,
            "optimization": 95,
            "completed": 100,
            "error": 0
        }
        
        return stage_progress.get(current_stage, 0)
