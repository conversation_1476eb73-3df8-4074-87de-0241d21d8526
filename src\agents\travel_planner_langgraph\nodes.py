"""
TravelPlannerAgent LangGraph节点函数

实现了旅行规划Agent的所有节点函数，支持双模运行和状态管理。
集成NotificationService以支持实时推送。
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .state import TravelPlanState, ProcessingStage, PlanningMode, add_event_to_state, update_state_stage
from .notification_service import NotificationService
from ..services.analysis_service import AnalysisService
from ..services.user_profile_service import UserProfileService
from ..services.amap_service import AmapService
from ..services.reasoning_service import ReasoningService
from ..services.memory_service import MemoryService
from src.services.user_profile_database_service import get_user_profile_database_service
from src.prompts.loader import apply_prompt_template
from src.prompts import apply_prompt_template

logger = logging.getLogger(__name__)


async def core_intent_analyzer_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 核心意图分析节点

    分析用户的原始查询，提取核心旅行意图和关键信息。
    通过NotificationService发布事件。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")

    logger.info(f"[V2] 开始核心意图分析 - Task ID: {task_id}")

    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="core_intent_analysis",
        title="解析用户需求",
        message="正在分析您的旅行意图和历史偏好..."
    )

    try:
        # 获取用户的真实数据库画像
        user_profile_db_service = get_user_profile_database_service()
        comprehensive_profile = await user_profile_db_service.get_user_comprehensive_profile(state["user_id"])
        state["comprehensive_user_profile"] = comprehensive_profile

        # 格式化用户画像为分析文本
        user_profile_text = user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile)
        logger.info(f"用户 {state['user_id']} 数据库画像完整度: {comprehensive_profile.get('profile_completeness', 0):.2f}")

        # 构建提示词变量
        template_vars = {
            "original_query": state["original_query"],
            "user_profile_text": user_profile_text,
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "analysis_context": comprehensive_profile.get("analysis_context", {})
        }
        messages = apply_prompt_template("travel_planner/01_core_intent_analyzer", template_vars)

        # 调用推理服务
        reasoning_service = ReasoningService(llm_role="basic")
        core_intent = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="core_intent_schema"
        )
        state["core_intent"] = core_intent

        # 准备向后兼容和前端展示的数据
        state["user_profile"] = comprehensive_profile.get("user_profile")
        state["user_memories"] = comprehensive_profile.get("user_memories", [])
        state["travel_preferences"] = comprehensive_profile.get("travel_preferences")
        
        logger.info(f"[V2] 核心意图分析完成 - 目的地: {core_intent.get('destinations')}")
        
        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="core_intent_analysis",
            status="success",
            result=core_intent
        )

    except Exception as e:
        error_message = f"核心意图分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "core_intent_analysis")
        # 抛出异常以停止工作流
        raise

    return state


async def poi_preference_analyzer_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 景点偏好分析节点

    分析用户的景点偏好类型和深度要求。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")
    
    logger.info(f"[V2] 开始景点偏好分析 - Task ID: {task_id}")
    
    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="poi_preference_analysis",
        title="分析景点偏好",
        message="正在分析您的景点偏好..."
    )
    
    try:
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})

        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": comprehensive_profile.get("user_memories", []),
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }
        messages = apply_prompt_template("travel_planner/02_attraction_preference_analyzer", template_vars)

        reasoning_service = ReasoningService(llm_role="basic")
        poi_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="attraction_preference_schema"
        )
        state["poi_preferences"] = poi_preferences
        
        logger.info(f"[V2] 景点偏好分析完成 - Task ID: {task_id}")

        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="poi_preference_analysis",
            status="success",
            result=poi_preferences
        )

    except Exception as e:
        error_message = f"景点偏好分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "poi_preference_analysis")
        raise
        
    return state


async def food_preference_analyzer_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 美食偏好分析节点

    分析用户的美食偏好和餐厅类型要求。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")

    logger.info(f"[V2] 开始美食偏好分析 - Task ID: {task_id}")

    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="food_preference_analysis",
        title="分析美食偏好",
        message="正在分析您的美食偏好..."
    )

    try:
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})
        
        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": comprehensive_profile.get("user_memories", []),
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }
        messages = apply_prompt_template("travel_planner/03_food_preference_analyzer", template_vars)

        reasoning_service = ReasoningService(llm_role="basic")
        food_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="food_preference_schema"
        )
        state["food_preferences"] = food_preferences
        
        logger.info(f"[V2] 美食偏好分析完成 - Task ID: {task_id}")
        
        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="food_preference_analysis",
            status="success",
            result=food_preferences
        )

    except Exception as e:
        error_message = f"美食偏好分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "food_preference_analysis")
        raise
        
    return state


async def accommodation_preference_analyzer_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 住宿偏好分析节点

    分析用户的住宿偏好和预算要求。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")
    
    logger.info(f"[V2] 开始住宿偏好分析 - Task ID: {task_id}")
    
    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="accommodation_preference_analysis",
        title="分析住宿偏好",
        message="正在分析您的住宿偏好..."
    )

    try:
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})
        
        template_vars = {
            "core_intent": core_intent,
            "user_profile": comprehensive_profile,
            "user_memories": comprehensive_profile.get("user_memories", []),
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }
        messages = apply_prompt_template("travel_planner/04_accommodation_preference_analyzer", template_vars)

        reasoning_service = ReasoningService(llm_role="basic")
        accommodation_preferences = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="accommodation_preference_schema"
        )
        state["accommodation_preferences"] = accommodation_preferences

        logger.info(f"[V2] 住宿偏好分析完成 - Task ID: {task_id}")
        
        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="accommodation_preference_analysis",
            status="success",
            result=accommodation_preferences
        )

    except Exception as e:
        error_message = f"住宿偏好分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "accommodation_preference_analysis")
        raise
        
    return state


async def multi_city_strategy_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 多城市策略节点

    如果检测到多个目的地，则分析城市间的最佳游玩顺序。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")
    
    logger.info(f"[V2] 开始多城市策略分析 - Task ID: {task_id}")
    
    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="multi_city_strategy_analysis",
        title="规划多城市策略",
        message="正在分析多城市游玩顺序..."
    )

    try:
        core_intent = state.get("core_intent", {})
        
        template_vars = {
            "destinations": core_intent.get("destinations"),
            "origin": core_intent.get("origin"),
            "days": core_intent.get("days")
        }
        messages = apply_prompt_template("travel_planner/01a_multi_city_strategy_analyzer", template_vars)

        reasoning_service = ReasoningService(llm_role="basic")
        multi_city_strategy = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="multi_city_strategy_schema"
        )
        state["multi_city_strategy"] = multi_city_strategy
        
        logger.info(f"[V2] 多城市策略分析完成 - 推荐顺序: {multi_city_strategy.get('recommended_order')} - Task ID: {task_id}")
        
        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="multi_city_strategy_analysis",
            status="success",
            result=multi_city_strategy
        )
        
    except Exception as e:
        error_message = f"多城市策略分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "multi_city_strategy_analysis")
        raise
        
    return state


async def driving_context_analyzer_node(state: TravelPlanState, config: dict) -> TravelPlanState:
    """
    [V2] 驾驶情境分析节点

    分析用户的驾驶相关需求，如续航、充电偏好等。
    """
    notification_service: NotificationService = config["configurable"].get("notification_service")
    task_id = config["configurable"].get("session_id")
    
    logger.info(f"[V2] 开始驾驶情境分析 - Task ID: {task_id}")

    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="driving_context_analysis",
        title="分析驾驶情境",
        message="正在分析您的驾驶相关需求..."
    )

    try:
        core_intent = state.get("core_intent", {})
        vehicle_info = state.get("vehicle_info", {})

        template_vars = {
            "core_intent": core_intent,
            "vehicle_info": vehicle_info
        }
        messages = apply_prompt_template("travel_planner/01b_driving_context_analyzer", template_vars)

        reasoning_service = ReasoningService(llm_role="basic")
        driving_context = await reasoning_service.analyze_with_structured_output(
            messages=messages,
            response_format="driving_context_schema"
        )
        state["driving_context"] = driving_context
        
        logger.info(f"[V2] 驾驶情境分析完成 - 驾驶策略: {driving_context.get('driving_strategy')} - Task ID: {task_id}")

        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="driving_context_analysis",
            status="success",
            result=driving_context
        )

    except Exception as e:
        error_message = f"驾驶情境分析失败: {str(e)}"
        logger.error(f"[V2] {error_message} - Task ID: {task_id}")
        state["has_error"] = True
        state["error_message"] = error_message
        await notification_service.notify_error(task_id, error_message, "driving_context_analysis")
        raise
        
    return state


async def preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """
    偏好分析节点
    
    分析用户的景点、美食等偏好，构建偏好画像。
    """
    logger.info(f"开始偏好分析 - Session: {state['session_id']}")
    
    try:
        # 添加开始事件
        state = add_event_to_state(state, "stage_start", {
            "stage": "preference_analysis",
            "message": "正在分析您的旅行偏好..."
        })
        
        core_intent = state.get("core_intent", {})
        comprehensive_profile = state.get("comprehensive_user_profile", {})

        # 获取用户的旅行偏好数据（从dh_tripplanner数据库）
        user_profile_db_service = get_user_profile_database_service()
        travel_preferences = await user_profile_db_service.get_user_travel_preferences(state["user_id"])

        logger.info(f"用户 {state['user_id']} 旅行偏好: {travel_preferences}")

        # 分析景点偏好（使用真实数据库数据）
        attraction_template_vars = {
            "core_intent": core_intent,
            "user_profile_text": user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile),
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "travel_preferences": travel_preferences,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days")
        }

        attraction_messages = apply_prompt_template(
            "travel_planner/02_attraction_preference_analyzer",
            attraction_template_vars
        )

        # 分析美食偏好（使用真实数据库数据）
        food_template_vars = {
            "core_intent": core_intent,
            "user_profile_text": user_profile_db_service.format_user_profile_for_analysis(comprehensive_profile),
            "user_memories": comprehensive_profile.get("user_memories", []),
            "travel_history": comprehensive_profile.get("travel_history", []),
            "travel_preferences": travel_preferences,
            "destinations": core_intent.get("destinations"),
            "days": core_intent.get("days"),
            "travelers": core_intent.get("travelers")
        }
        
        food_messages = apply_prompt_template(
            "travel_planner/03_food_preference_analyzer",
            food_template_vars
        )
        
        # 调用推理服务 - 意图理解阶段使用basic模型
        reasoning_service = ReasoningService(llm_role="basic")

        # 流式分析景点偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "attraction_analysis",
            "message": "正在分析景点偏好类型..."
        })

        attraction_preferences = await reasoning_service.analyze_with_structured_output(
            messages=attraction_messages,
            response_format="attraction_preference_schema"
        )

        # 景点偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "attraction_analysis",
            "result": attraction_preferences,
            "message": "景点偏好分析完成"
        })

        # 流式分析美食偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "food_analysis",
            "message": "正在分析美食偏好..."
        })

        food_preferences = await reasoning_service.analyze_with_structured_output(
            messages=food_messages,
            response_format="food_preference_schema"
        )

        # 美食偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "food_analysis",
            "result": food_preferences,
            "message": "美食偏好分析完成"
        })

        # 流式分析住宿偏好
        state = add_event_to_state(state, "step_progress", {
            "step": "accommodation_analysis",
            "message": "正在分析住宿偏好..."
        })

        # 基于用户画像和意图生成住宿偏好（简化版，不调用LLM）
        accommodation_preferences = {
            "budget_range": core_intent.get("budget", {}).get("range", "暂无"),
            "accommodation_type": travel_preferences.get("accommodation_pref", "暂无") if travel_preferences else "暂无",
            "special_requirements": ["免费停车", "安静整洁"] if travel_preferences else ["暂无"],
            "confidence_score": 0.8 if travel_preferences else 0.3
        }

        # 住宿偏好分析完成
        state = add_event_to_state(state, "step_completed", {
            "step": "accommodation_analysis",
            "result": accommodation_preferences,
            "message": "住宿偏好分析完成"
        })

        # 构建偏好画像
        preference_profile = {
            "attraction_preferences": attraction_preferences,
            "food_preferences": food_preferences,
            "accommodation_preferences": accommodation_preferences,
            "confidence_score": (
                attraction_preferences.get("confidence_score", 0) +
                food_preferences.get("confidence_score", 0) +
                accommodation_preferences.get("confidence_score", 0)
            ) / 3
        }
        
        state["preference_profile"] = preference_profile
        
        # 添加完成事件
        state = add_event_to_state(state, "preferences_analyzed", {
            "attraction_confidence": attraction_preferences.get("confidence_score", 0),
            "food_confidence": food_preferences.get("confidence_score", 0),
            "overall_confidence": preference_profile["confidence_score"]
        })
        
        logger.info("偏好分析完成")
        
    except Exception as e:
        logger.error(f"偏好分析失败: {str(e)}")
        state["has_error"] = True
        state["error_message"] = f"偏好分析失败: {str(e)}"
        state = add_event_to_state(state, "error", {
            "stage": "preference_analysis",
            "error": str(e)
        })
    
    return state


def should_analyze_multi_city(state: TravelPlanState) -> str:
    """判断是否需要多城市策略分析"""
    core_intent = state.get("core_intent", {})
    destinations = core_intent.get("destinations", [])
    
    if len(destinations) > 1:
        return "multi_city_strategy"
    else:
        return "driving_context"


def should_analyze_driving_context(state: TravelPlanState) -> str:
    """判断是否需要驾驶情境分析"""
    core_intent = state.get("core_intent", {})
    transportation = core_intent.get("transportation", {})
    
    if transportation.get("primary_mode") == "self_driving":
        return "driving_context"
    else:
        return "preference_analysis"


def has_error(state: TravelPlanState) -> str:
    """检查是否有错误"""
    if state.get("has_error", False):
        return "error_handler"
    else:
        return "continue"
