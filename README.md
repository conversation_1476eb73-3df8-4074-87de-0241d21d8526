# AutoPilot AI - 智能旅行规划Agent系统

一个基于Python的智能旅行规划Agent系统，支持多种LLM后端，具备强大的个性化推荐能力和完整的数据库驱动架构。

## 🌟 主要特性

### 🚀 旅行规划核心能力
- **智能意图理解**：基于LLM的自然语言理解，准确提取旅行需求
- **个性化画像系统**：MySQL驱动的用户记忆和偏好学习
- **四阶段规划流程**：意图理解→工具执行→智能决策→结果生成
- **实时流式输出**：SSE技术展示AI思考过程和规划进度
- **高德地图集成**：完整的地理服务、POI推荐、路线规划

### 🧠 个性化智能系统
- **记忆驱动推荐**：从用户历史记忆中提取偏好和旅行习惯
- **智能POI评分**：基于用户画像的多维度评分算法
- **自动学习机制**：每次规划自动保存用户记忆，持续优化推荐
- **偏好模式识别**：智能分析用户的活动偏好、预算习惯、目的地偏好

### 🏗️ 技术架构
- **多数据库架构**：MySQL用户画像 + MongoDB行程存储 + Redis缓存
- **统一配置管理**：基于Pydantic的类型安全配置系统
- **多LLM支持**：支持智谱AI、OpenAI等多种模型提供商
- **微服务设计**：模块化组件，支持独立扩展
- **完整监控体系**：结构化日志、性能监控、分布式追踪

### 🔧 开发体验
- **TDD开发模式**：完整的单元测试和集成测试覆盖
- **类型安全**：全面的类型注解和mypy检查
- **热重载开发**：支持开发环境快速迭代
- **容器化部署**：Docker支持，一键部署

## 🚀 快速开始

### 1. 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd autopilotai2

# 创建虚拟环境
python -m venv .venv
.\.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -e .
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# AI模型配置
BASIC_LLM_MODEL=glm-4-flash
BASIC_LLM_API_KEY=your-zhipu-api-key
BASIC_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

REASONING_LLM_MODEL=glm-z1-flash
REASONING_LLM_API_KEY=your-zhipu-api-key
REASONING_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

# 高德地图API
AMAP_API_KEY=your-amap-api-key

# 数据库配置
MYSQL_HOST=your-mysql-host
MYSQL_PORT=19090
MYSQL_USER=root
MYSQL_PASSWORD=your-password

MONGODB_HOST=your-mongodb-host
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=your-password
```

### 3. 启动服务

```bash
# 检查环境
python start_server.py --check-only

# 启动开发服务器
python start_server.py --reload

# 访问系统
# 前端: http://localhost:8000/static/index.html
# API文档: http://localhost:8000/docs
```

## 📚 使用示例

### 旅行规划API调用 (V2 事件驱动)

```python
import asyncio
import httpx
import json

async def plan_travel_v2():
    # V2 API使用单个POST请求直接获取事件流
    async with httpx.AsyncClient(timeout=None) as client:
        # 请求体与旧版兼容
        request_body = {
            "user_id": "user_001",
            "query": "我想去西安玩3天，喜欢历史文化景点和美食"
        }
        
        # 直接向V2流式API发送POST请求
        async with client.stream(
            "POST", 
            "http://localhost:8000/api/v2/plan/stream",
            json=request_body
        ) as stream:
            async for line in stream.aiter_lines():
                # 解析SSE事件
                if line.startswith("data: "):
                    try:
                        # 去掉 "data: " 前缀并解析JSON
                        event_data = json.loads(line[6:])
                        event_type = event_data.get("event")
                        data = event_data.get("data")
                        
                        print(f"事件类型: {event_type}")
                        
                        if data:
                            # 打印部分数据以保持简洁
                            data_str = str(data)
                            if len(data_str) > 150:
                                data_str = data_str[:150] + "..."
                            print(f"事件数据: {data_str}")

                        # 当收到最终行程事件时，可以结束监听
                        if event_type == 'final_itinerary':
                            print("\n🎉 最终行程生成完成!")
                            print(json.dumps(data, indent=2, ensure_ascii=False))
                            break
                    except json.JSONDecodeError:
                        print(f"无法解析的行: {line}")

asyncio.run(plan_travel_v2())
```

### 直接使用TravelPlannerAgent (V2 事件驱动)

```python
import asyncio
from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.models.travel_planner import TravelPlanRequest

async def main():
    # 1. 创建LangGraph Agent实例
    # 这个Agent是项目当前的核心实现
    agent = TravelPlannerAgentLangGraph()
    
    # 2. 创建规划请求
    request = TravelPlanRequest(
        user_id="user_001",
        query="我想去北京玩4天，预算5000元，喜欢历史建筑和传统文化"
    )
    
    # 3. 运行规划流程 (V2 事件驱动模型)
    # 这个方法会启动一个后台任务来执行规划，并通过Redis发布事件。
    # 它本身会立即返回，不会像旧方法一样流式返回结果。
    # 这种“即发即忘”的模式将业务逻辑与通知完全解耦。
    print("🚀 开始异步规划任务...")
    task_id = await agent.run_planning_with_notifications(request=request)
    print(f"✅ 规划任务已在后台启动 (Task ID: {task_id})。")
    print("请通过V2 API端点或直接监听Redis Pub/Sub来接收实时事件。")
    
    # 在实际应用中，启动任务的进程和消费事件的进程是分离的。
    # 这个脚本启动任务后就会退出。你需要一个像FastAPI服务器这样的
    # 消费者来监听Redis频道 `task:{task_id}` 上的事件。

if __name__ == "__main__":
    asyncio.run(main())
```

## 🏗️ 项目架构 (V2 事件驱动)

```
autopilotai/
├── src/                          # 主要源码
│   ├── agents/                   # Agent实现
│   │   └── travel_planner_langgraph/  # 核心LangGraph Agent
│   │       ├── graph.py           # LangGraph图定义
│   │       ├── nodes.py           # 业务逻辑节点
│   │       ├── state.py           # Agent状态
│   │       └── ...
│   ├── services/                 # 应用服务
│   │   └── notification_service.py # Redis事件发布服务
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   └── llm_manager.py       # LLM管理器
│   ├── models/                   # 数据模型
│   │   ├── mysql_models.py      # MySQL数据库模型
│   │   └── mysql_crud.py        # 数据库CRUD操作
│   ├── database/                 # 数据库层
│   │   └── redis_client.py      # Redis连接
│   ├── tools/                    # 工具集成
│   └── api/                      # API路由
│       └── travel_planner.py    # 旅行规划API (V2)
├── tests/                        # 测试套件
│   └── integration/
│       └── test_v2_event_flow.py # V2架构集成测试
├── config/                       # 配置文件
└── static/                       # 前端静态文件
```

## 🧠 核心组件详解 (LangGraph & 事件驱动)

项目的核心规划逻辑已经重构为基于`LangGraph`的图驱动模型，并通过Redis Pub/Sub实现彻底解耦的事件通知，取代了旧的线性四阶段流程。

#### 1. AgentState - 统一状态管理
`AgentState` (`src/agents/travel_planner_langgraph/state.py`) 是整个规划流程的核心数据容器。它像一个“数据护照”，在图的每个节点之间传递，并不断被丰富。所有中间结果，如意图分析、POI列表、用户画像等，都保存在这个State对象中。

#### 2. LangGraph 节点 - 原子化业务逻辑
每个业务处理步骤都被封装成一个独立的函数，称为“节点”（`src/agents/travel_planner_langgraph/nodes.py`）。例如：
- `analyze_intent_node`: 分析用户意图。
- `poi_search_node`: 并行执行POI搜索。
- `generate_itinerary_node`: 生成最终行程。

每个节点接收当前的`AgentState`，执行其特定任务，然后将结果写回`AgentState`，再将其传递给图的下一个节点。

#### 3. NotificationService - 解耦的事件发布器
为了实现真正的实时反馈，我们引入了`NotificationService` (`src/services/notification_service.py`)。业务节点（Nodes）在执行关键操作（如开始、结束、出错）时，不再直接返回状态，而是调用此服务来“宣告”一个事件。

```python
# 在 nodes.py 中的某个节点:
async def some_node(state: AgentState):
    # ...
    await self.notification_service.publish(
        task_id=state['task_id'],
        event="some_node_started",
        data={"message": "某项任务已开始"}
    )
    # ... 执行业务逻辑 ...
    await self.notification_service.publish(
        task_id=state['task_id'],
        event="some_node_completed",
        data={"result": "...some data..."}
    )
    return state
```

#### 4. Redis Pub/Sub - 事件消息总线
`NotificationService` 将事件发布到Redis的特定频道（例如 `task:your_task_id`）。这种发布/订阅模式允许任意数量的消费者（如我们的V2 API端点）实时监听这些事件，而无需与Agent执行逻辑有任何直接联系。这彻底解决了先前版本中业务执行阻塞事件推送的问题。

#### 5. Graph - 流程编排
`graph.py` 文件负责将所有独立的节点（Nodes）组装成一个有向图，定义了它们之间的执行顺序和逻辑分支（例如，根据意图分析结果决定下一步是搜索景点还是酒店）。`LangGraph`会根据这个定义来执行整个规划流程。

## �� 支持的服务

### AI模型服务
- **智谱AI glm-z1-flash**: 复杂推理和决策
- **智谱AI glm-4-flash**: 快速交互和格式化
- **OpenAI系列**: 计划支持（GPT-4、GPT-3.5-turbo等）

### 数据库服务
- **MySQL 8.0+**: 用户画像和记忆系统
- **MongoDB 4.4+**: 行程文档存储
- **Redis 6.0+**: 缓存和会话管理

### 地图服务
- **高德地图API**: 地理编码、POI搜索、路线规划
- **MCP接口**: https://mcp.amap.com/sse

## 🧪 测试体系

### 测试覆盖
- **MySQL模型测试**: 数据模型定义、CRUD操作、字段验证
- **Agent功能测试**: 四阶段流程、个性化算法、偏好提取
- **集成测试**: 端到端流程验证、数据库集成测试
- **API测试**: HTTP接口、SSE流式输出

### 运行测试
```bash
# MySQL模型集成测试
python tests/scripts/test_mysql_models_integration.py

# Agent个性化功能测试
python tests/scripts/test_agent_simple.py

# 数据库集成测试
python tests/scripts/test_agent_database_basic.py

# 完整API测试
pytest tests/integration/ -v
```

## 📖 文档资源

- [快速开始指南](QUICK_START_GUIDE.md) - 详细的安装和使用指南
- [数据库架构文档](src/models/mysql_models.py) - MySQL数据模型说明
- [Agent改造总结](改造完成总结.md) - 个性化功能改造记录
- [API接口文档](http://localhost:8000/docs) - Swagger自动生成文档

## 🛠️ 开发指南

### 开发环境搭建
```bash
# 安装开发依赖
pip install -e ".[dev]"

# 代码格式化
black src/ tests/

# 类型检查
mypy src/

# 运行所有测试
pytest tests/ -v --cov=src
```

### 添加新功能
1. **扩展用户画像**: 在 `src/models/mysql_models.py` 中添加新字段
2. **优化个性化算法**: 修改 `src/agents/travel_planner_agent.py` 中的评分逻辑
3. **集成新工具**: 在 `src/tools/` 目录下添加新的工具客户端
4. **扩展API**: 在 `src/api/` 目录下添加新的路由

### 贡献指南
1. Fork项目并创建特性分支
2. 编写测试用例，确保覆盖率
3. 遵循代码规范，通过类型检查
4. 更新相关文档
5. 提交Pull Request

## 📊 项目状态

| 模块 | 状态 | 测试覆盖率 | 说明 |
|------|------|------------|------|
| 配置管理 | ✅ 完成 | 100% | 多环境配置，类型安全 |
| 日志系统 | ✅ 完成 | 99% | 结构化日志、分布式追踪 |
| LLM管理器 | ✅ 完成 | 90% | 支持多种LLM后端 |
| MySQL用户画像 | ✅ 完成 | 95% | 完整的CRUD和模型系统 |
| 旅行规划Agent | ✅ 完成 | 90% | 四阶段流程，个性化推荐 |
| 高德地图集成 | ✅ 完成 | 85% | 完整的地理服务API |
| Web API服务 | ✅ 完成 | 80% | FastAPI + SSE流式输出 |
| 前端界面 | ✅ 完成 | 70% | 基础交互界面 |

## 🔮 规划路线

### 已完成（第一阶段）
- ✅ 核心Agent框架和四阶段流程
- ✅ MySQL用户画像和记忆系统
- ✅ 个性化POI推荐算法
- ✅ 高德地图API完整集成
- ✅ 流式SSE输出和Web界面
- ✅ 完整的测试体系

### 进行中（第二阶段）
- 🔄 MongoDB连接优化和错误修复
- 🔄 个性化算法效果评估和优化
- 🔄 更丰富的偏好提取模式
- 🔄 性能监控和数据库索引优化

### 计划中（第三阶段）
- 📋 多Agent协作（专家Agent、决策Agent等）
- 📋 用户反馈学习机制
- 📋 A/B测试框架
- 📋 移动端适配和小程序集成
- 📋 企业级部署方案

## 🌟 技术亮点

### 个性化推荐系统
- **记忆驱动**: 基于用户历史记忆的智能偏好提取
- **多维度评分**: 综合用户画像、记忆匹配、基础评分的算法
- **自动学习**: 每次规划自动沉淀新知识，持续优化

### 智能决策引擎
- **图驱动的复杂工作流**: 使用`LangGraph`编排业务节点，支持条件分支和循环，能够执行高度复杂的、非线性的规划任务。
- **动态权重**: 根据用户偏好动态调整不同因素的权重
- **上下文感知**: 基于天气、时间、用户状态的智能决策

### 企业级架构
- **事件驱动与解耦**: 通过Redis Pub/Sub实现事件总线，将核心业务逻辑与前端通知完全解耦，提升了系统的响应能力和可扩展性。
- **微服务设计**: 模块化组件，支持独立扩展
- **多数据库**: MySQL画像 + MongoDB存储 + Redis缓存
- **容错机制**: 完善的异常处理和降级策略

## 📞 支持

### 开发团队
- 📧 邮箱: <EMAIL>
- 💬 技术交流群: AutoPilot-AI-Dev
- 🐛 Issue跟踪: GitHub Issues
- 📖 技术博客: [待开通]

### 商业合作
- 📧 商务邮箱: <EMAIL>
- 📞 合作热线: 400-XXX-XXXX
- 🤝 企业定制: 支持定制化开发
- 🌐 官方网站: [待开通]

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**AutoPilot AI** - 智能旅行规划的未来，个性化推荐的典范

🎯 **核心价值**: 让每一次旅行规划都是独一无二的个性化体验

🚀 **技术愿景**: 构建最智能、最懂用户的AI旅行规划系统