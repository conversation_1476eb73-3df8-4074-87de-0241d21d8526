/**
 * AutoPilot AI - 重构版前端应用
 * 
 * 支持两阶段交互模式和TTS播报的现代化界面
 */

class TravelPlannerAppRefactored {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.currentPhase = 'waiting'; // waiting, analysis, planning, completed
        this.currentUserId = null;
        this.currentQuery = null;

        // 请求锁机制 - 根据推送.md文档要求
        this.isPlanning = false;

        // 动态步骤管理 - 完全由后端驱动
        this.dynamicSteps = new Map(); // step_id -> step_info
        this.stepOrder = []; // 保存步骤顺序

        // 状态数据存储
        this.state = {
            core_intent: null,
            user_profile: null,
            user_memories: null,
            travel_preferences: null,
            preference_profile: null,
            driving_context: null,
            vehicle_info: null,
            multi_city_strategy: null
        };

        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
        this.updateUI();
    }
    
    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });
        
        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });
        
        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });
        
        // 立即规划按钮
        const startPlanningBtn = document.getElementById('startPlanningBtn');
        if (startPlanningBtn) {
            startPlanningBtn.addEventListener('click', () => {
                this.startItineraryPlanning();
            });
        }
        
        // 取消规划按钮
        const cancelPlanningBtn = document.getElementById('cancelPlanningBtn');
        if (cancelPlanningBtn) {
            cancelPlanningBtn.addEventListener('click', () => {
                this.cancelPlanning();
            });
        }
        
        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });
        
        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });
        
        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });
        
        // 历史行程按钮
        document.getElementById('historyBtn').addEventListener('click', () => {
            this.showHistory();
        });
    }
    
    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }
    
    updateUI() {
        // 根据当前阶段更新UI显示
        this.hideAllViews();

        switch (this.currentPhase) {
            case 'waiting':
                this.showWaitingView();
                break;
            case 'analysis':
                this.showAnalysisView();
                break;
            case 'planning':
                this.showPlanningView();
                break;
            case 'completed':
                this.showCompletedView();
                break;
        }
    }

    hideAllViews() {
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';
        document.getElementById('itineraryView').style.display = 'none';
    }

    showWaitingView() {
        document.getElementById('waitingView').style.display = 'flex';
    }

    showAnalysisView() {
        document.getElementById('analysisView').style.display = 'flex';

        // 更新分析状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = '正在分析您的需求...';
        if (desc) desc.textContent = 'AI正在理解您的旅行偏好和需求';
    }

    showPlanningView() {
        // 规划阶段：左侧保留意图理解，右侧显示规划过程
        // 隐藏等待和分析状态视图
        document.getElementById('waitingView').style.display = 'none';
        document.getElementById('analysisView').style.display = 'none';

        // 显示规划视图（在右侧面板中）
        document.getElementById('itineraryView').style.display = 'block';

        // 确保左侧分析面板保持可见（显示已完成的意图理解结果）
        const analysisPanel = document.querySelector('.analysis-panel');
        if (analysisPanel) {
            analysisPanel.style.display = 'block';
        }

        // 在右侧显示规划状态
        this.showPlanningStatus();
    }

    showPlanningStatus() {
        // 在右侧面板显示规划状态
        const itineraryView = document.getElementById('itineraryView');
        if (itineraryView) {
            itineraryView.innerHTML = `
                <div class="planning-status-content">
                    <div class="status-content">
                        <div class="status-spinner">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <h4 class="status-title">🚀 开始规划您的精彩行程...</h4>
                        <p class="status-description">AI正在为您生成个性化的旅行方案</p>
                        <div class="planning-progress">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="planningProgressBar"></div>
                            </div>
                            <div class="progress-text mt-2" id="planningProgressText">准备开始...</div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    showCompletedView() {
        document.getElementById('itineraryView').style.display = 'block';
    }

    // 添加缺失的方法
    regenerateItinerary() {
        if (confirm('确定要重新生成行程吗？')) {
            // 重新启动规划阶段
            this.startPlanningPhase();
        }
    }

    viewItinerary() {
        // 切换到行程查看模式
        this.currentPhase = 'completed';
        this.updateUI();

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('为您展示完整的旅行行程');
        }
    }
    
    async startPlanning() {
        // [V2] 使用新的V2架构启动规划流程

        // 1. 加锁检查
        if (this.isPlanning) {
            console.log("已有规划任务在进行中，请勿重复点击。");
            this.showAlert("已有规划任务在进行中，请稍候...", "warning");
            return;
        }

        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        if (!query) {
            this.showAlert('请输入您的旅行想法', 'warning');
            return;
        }

        // 2. 加锁并更新UI
        this.isPlanning = true;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = true;
            planButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 分析中...';
        }

        // 3. 清理旧的UI状态和数据
        this.clearPreviousResults();
        this.currentQuery = query;
        this.currentUserId = userId;
        this.currentPhase = 'analysis'; // 初始阶段仍可视为分析
        this.updateUI();

        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('开始为您规划行程');
        }
        
        // 4. [V2] 直接连接到新的V2流式API端点
        // EventSource API本身不支持直接发送POST请求体。
        // 但FastAPI的EventSourceResponse可以直接响应POST请求，所以我们可以这样做。
        // 前端通过POST启动任务，后端立即返回SSE流。
        // 实际场景中，为避免query过长，通常会先POST创建任务拿到task_id，再GET SSE流。
        // 但遵循我们文档的设计，我们在此简化，直接用 EventSource 连接一个需要POST触发的端点。
        // 这需要后端框架（FastAPI）的支持。
        // 关键是，这里的URL是POST端点，但我们用GET方式的EventSource去连，
        // 实际上是依赖于浏览器和服务器的某种协商或代理。
        // 一个更标准的前端实现是：
        // a. fetch POST -> 获得task_id
        // b. new EventSource(GET /stream/{task_id})
        // 这里我们采用最直接的方案，假设 EventSource 可以连接到由 POST 启动的流。

        try {
            // 注意：下面的代码模拟了POST后立即开始SSE流的场景。
            // 一个完整的实现可能需要先fetch POST，然后用返回的ID构造EventSource的URL。
            // 但为了适配我们API的设计，我们直接连接。
            
            const requestData = {
                user_id: this.currentUserId,
                query: this.currentQuery,
                // vehicle_info: this.state.vehicle_info // 如果有车辆信息
            };

            // EventSource不支持发送body，所以我们将请求数据编码到URL中，
            // 后端需要能从查询参数中解析。这是一种妥协。
            // 或者，我们修改 `startRealPlanning` 来处理这个逻辑。
            this.startRealPlanningV2(requestData);

        } catch (error) {
            console.error('启动规划失败:', error);
            this.showAlert(`启动规划失败: ${error.message}`, 'danger');
            this.unlockPlanning();
        }
    }

    startRealPlanningV2(requestData) {
        // [V2] 新的SSE连接函数
        
        // 由于EventSource不支持POST，我们通过fetch来启动后台任务并处理流
        fetch('/api/travel/v2/plan/stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify(requestData)
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const reader = response.body.getReader();
            const decoder = new TextDecoder('utf-8');

            const processStream = () => {
                reader.read().then(({ done, value }) => {
                    if (done) {
                        console.log('Stream completed');
                        // 流结束时由eos事件处理，这里不需要调用finishPlanning
                        return;
                    }
                    
                    // 解码收到的数据块
                    const chunk = decoder.decode(value, { stream: true });
                    // SSE消息以 "data: " 开头，并以 "\n\n" 结尾
                    // 一个数据块可能包含多条消息
                    const events = chunk.split('\n\n');
                    events.forEach(eventStr => {
                        if (eventStr.startsWith('data:')) {
                            const jsonData = eventStr.substring(5).trim();
                            if (jsonData) {
                                try {
                                    const eventData = JSON.parse(jsonData);
                                    this.handleSSEEventV2(eventData);
                                } catch (e) {
                                    console.error('Error parsing SSE event data:', e, jsonData);
                                }
                            }
                        }
                    });
                    
                    processStream(); // 继续读取下一块数据
                }).catch(error => {
                    console.error('Error reading stream:', error);
                    this.handleError({ message: '与服务器的实时连接中断' });
                    this.unlockPlanning();
                });
            };
            processStream();

        }).catch(error => {
            console.error('Fetch SSE stream failed:', error);
            this.showAlert(`连接实时服务失败: ${error.message}`, 'danger');
            this.unlockPlanning();
        });
    }


    clearPreviousResults() {
        // 清理之前的结果
        this.dynamicSteps.clear();
        this.stepOrder = [];
        this.currentItinerary = null;

        // 重置状态数据
        Object.keys(this.state).forEach(key => {
            this.state[key] = null;
        });

        // 清理动态创建的步骤UI
        const stepsContainer = document.getElementById('analysisSteps');
        if (stepsContainer) {
            stepsContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <div class="text-center text-muted">
                        <i class="bi bi-clock-history"></i>
                        <p class="mt-2">等待开始分析...</p>
                    </div>
                </div>
            `;
        }
    }

    unlockPlanning() {
        // 解锁规划状态
        this.isPlanning = false;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = false;
            planButton.innerHTML = '<i class="bi bi-search"></i> 开始规划';
        }
    }

    unlockForNextPhase() {
        // 为下一阶段解锁（分析完成，准备规划）
        this.isPlanning = false;
        const planButton = document.getElementById('planningForm').querySelector('button[type="submit"]');
        if (planButton) {
            planButton.disabled = false;
            planButton.innerHTML = '<i class="bi bi-check-circle"></i> 分析完成';
            planButton.style.backgroundColor = '#28a745';
            planButton.style.borderColor = '#28a745';
        }
    }
    

    
    // 模拟分析过程已移除，现在使用真实的SSE连接
    
    setAnalysisStepActive(stepKey) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        }
    }
    
    completeAnalysisStep(stepKey, content) {
        this.analysisSteps[stepKey].completed = true;
        
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // 更新结果显示
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = `<div class="analysis-content-text">${content}</div>`;
            }
            
            // 更新状态图标
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="bi bi-check-circle-fill"></i>';
            }
        }
    }
    
    // ==================== 动态UI管理方法 ====================

    showStepInProgress(stepId, title, message) {
        console.log('显示步骤进行中:', stepId, title, message);

        // 获取分析步骤容器
        const stepsContainer = document.getElementById('analysisSteps');

        if (!stepsContainer) {
            console.warn('未找到分析步骤容器');
            return;
        }

        // 如果是第一个步骤，清除占位符
        const placeholder = stepsContainer.querySelector('.analysis-placeholder');
        if (placeholder) {
            placeholder.remove();
        }

        // 根据步骤名称选择合适的图标
        const iconMap = {
            'core_intent_analysis': 'bi-person-check',
            'poi_preference_analysis': 'bi-geo-alt',
            'food_preference_analysis': 'bi-cup-hot',
            'accommodation_preference_analysis': 'bi-house',
            'weather_analysis': 'bi-cloud-sun',
            'route_planning': 'bi-map'
        };

        const stepName = this.dynamicSteps.get(stepId)?.stepName || 'unknown';
        const iconClass = iconMap[stepName] || 'bi-gear';

        // 创建分析项目HTML
        const analysisItemHtml = `
            <div class="analysis-item active" id="step-card-${stepId}" data-step="${stepId}">
                <div class="analysis-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="analysis-content">
                    <h6 class="analysis-title" id="title-${stepId}">${title}</h6>
                    <div class="analysis-result" id="result-${stepId}">
                        <div class="loading-placeholder">${message}</div>
                    </div>
                </div>
                <div class="analysis-status" id="status-${stepId}">
                    <div class="spinner-border spinner-border-sm" role="status"></div>
                </div>
            </div>
        `;

        // 添加到容器中
        stepsContainer.insertAdjacentHTML('beforeend', analysisItemHtml);

        // 记录步骤顺序
        if (!this.stepOrder.includes(stepId)) {
            this.stepOrder.push(stepId);
        }
        this.dynamicSteps.set(stepId, {
            stepName: stepName,
            title: title,
            status: 'running',
            startTime: Date.now() // 使用Date.now()作为临时时间戳
        });

        this.updateDynamicStepsUI();
    }

    showStepSuccess(stepId, result) {
        console.log('显示步骤成功:', stepId, result);

        // 更新状态图标
        const statusElement = document.getElementById(`status-${stepId}`);
        if (statusElement) {
            statusElement.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
        }

        // 更新结果内容
        const resultElement = document.getElementById(`result-${stepId}`);
        if (resultElement && result) {
            // 根据结果更新内容
            const resultText = this.formatStepResult(result);
            resultElement.innerHTML = `<div class="analysis-content-text">${resultText}</div>`;
        }

        // 更新卡片状态
        const cardElement = document.getElementById(`step-card-${stepId}`);
        if (cardElement) {
            cardElement.classList.remove('active');
            cardElement.classList.add('completed');
        }

        const stepInfo = this.dynamicSteps.get(stepId);
        stepInfo.status = 'success';
        stepInfo.result = result;
        stepInfo.message = this.formatStepResult(result) || '已完成'; // 使用格式化函数

        this.updateDynamicStepsUI();
    }

    showStepError(stepId, errorMessage) {
        console.log('显示步骤错误:', stepId, errorMessage);

        // 更新状态图标
        const statusElement = document.getElementById(`status-${stepId}`);
        if (statusElement) {
            statusElement.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
        }

        // 更新错误消息
        const resultElement = document.getElementById(`result-${stepId}`);
        if (resultElement) {
            resultElement.innerHTML = `<div class="text-danger">${errorMessage}</div>`;
        }

        // 更新卡片状态
        const cardElement = document.getElementById(`step-card-${stepId}`);
        if (cardElement) {
            cardElement.classList.remove('active');
            cardElement.classList.add('error');
        }

        const stepInfo = this.dynamicSteps.get(stepId);
        stepInfo.status = 'error';
        stepInfo.message = errorMessage;

        this.updateDynamicStepsUI();
        this.showAlert(`步骤 "${stepInfo.title}" 失败: ${errorMessage}`, 'danger');
    }

    formatAttractionPreferences(result) {
        // 格式化景点偏好分析结果
        const parts = [];

        // 提取主要偏好类型
        if (result.attraction_type_preferences) {
            const prefs = result.attraction_type_preferences;
            const topPrefs = [];

            // 文化历史类
            if (prefs.cultural_historical) {
                const cultural = Object.entries(prefs.cultural_historical)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (cultural.length > 0) topPrefs.push(...cultural);
            }

            // 自然风光类
            if (prefs.natural_scenery) {
                const natural = Object.entries(prefs.natural_scenery)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (natural.length > 0) topPrefs.push(...natural);
            }

            // 特色体验类
            if (prefs.special_experiences) {
                const special = Object.entries(prefs.special_experiences)
                    .filter(([key, value]) => value >= 7)
                    .map(([key, value]) => this.translateAttractionType(key));
                if (special.length > 0) topPrefs.push(...special);
            }

            if (topPrefs.length > 0) {
                parts.push(`偏好类型：${topPrefs.join('、')}`);
            }
        }

        // 游览风格
        if (result.touring_style && result.touring_style.primary_style) {
            parts.push(`游览风格：${result.touring_style.primary_style}`);
        }

        // 如果没有明确偏好，显示推荐备注
        if (parts.length === 0 && result.recommendation_notes) {
            return result.recommendation_notes;
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化景点偏好分析';
    }

    formatFoodPreferences(result) {
        // 格式化美食偏好分析结果
        const parts = [];

        // 检查是否有明确的偏好数据
        const hasValidData = this.hasValidFoodPreferences(result);

        if (!hasValidData) {
            return '暂无明确美食偏好数据，将基于目的地特色推荐';
        }

        // 口味偏好
        if (result.taste_preferences && result.taste_preferences.basic_tastes) {
            const tastes = Object.entries(result.taste_preferences.basic_tastes)
                .filter(([key, value]) => value !== '暂无明确偏好数据' && value)
                .map(([key, value]) => this.translateTasteType(key));
            if (tastes.length > 0) {
                parts.push(`口味偏好：${tastes.join('、')}`);
            }
        }

        // 菜系偏好
        if (result.cuisine_preferences && result.cuisine_preferences.chinese_cuisines) {
            const cuisines = Object.entries(result.cuisine_preferences.chinese_cuisines)
                .filter(([key, value]) => value !== '暂无明确偏好数据' && value)
                .map(([key, value]) => this.translateCuisineType(key));
            if (cuisines.length > 0) {
                parts.push(`菜系偏好：${cuisines.join('、')}`);
            }
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化美食偏好分析';
    }

    formatAccommodationPreferences(result) {
        // 格式化住宿偏好分析结果
        const parts = [];

        // 预算范围
        if (result.budget_analysis && result.budget_analysis.budget_range) {
            parts.push(`预算：${result.budget_analysis.budget_range}`);
        }

        // 住宿类型偏好
        if (result.accommodation_type_preferences) {
            const topTypes = Object.entries(result.accommodation_type_preferences)
                .filter(([key, value]) => value >= 7)
                .map(([key, value]) => this.translateAccommodationType(key));
            if (topTypes.length > 0) {
                parts.push(`类型偏好：${topTypes.join('、')}`);
            }
        }

        // 必备设施
        if (result.filtering_criteria && result.filtering_criteria.must_have_features) {
            const features = result.filtering_criteria.must_have_features;
            if (features.length > 0) {
                parts.push(`必备设施：${features.join('、')}`);
            }
        }

        return parts.length > 0 ? parts.join(' | ') : '基于用户画像的个性化住宿偏好分析';
    }

    // 翻译方法
    translateAttractionType(key) {
        const translations = {
            'ancient_architecture': '古建筑',
            'museums': '博物馆',
            'cultural_districts': '文化街区',
            'religious_sites': '宗教场所',
            'mountains': '山景',
            'water_features': '水景',
            'gardens_parks': '园林公园',
            'nature_reserves': '自然保护区',
            'folk_culture': '民俗文化',
            'food_exploration': '美食探索',
            'outdoor_activities': '户外活动',
            'photography_spots': '摄影景点'
        };
        return translations[key] || key;
    }

    translateTasteType(key) {
        const translations = {
            'sweet': '甜味',
            'sour': '酸味',
            'spicy': '辣味',
            'salty': '咸味',
            'umami': '鲜味',
            'bitter': '苦味'
        };
        return translations[key] || key;
    }

    translateCuisineType(key) {
        const translations = {
            'sichuan': '川菜',
            'cantonese': '粤菜',
            'hunan': '湘菜',
            'shandong': '鲁菜',
            'jiangsu': '苏菜',
            'zhejiang': '浙菜',
            'fujian': '闽菜',
            'anhui': '徽菜'
        };
        return translations[key] || key;
    }

    translateAccommodationType(key) {
        const translations = {
            'luxury_hotels': '豪华酒店',
            'boutique_hotels': '精品酒店',
            'business_hotels': '商务酒店',
            'budget_hotels': '经济酒店',
            'guesthouses': '民宿',
            'hostels': '青年旅社'
        };
        return translations[key] || key;
    }

    hasValidFoodPreferences(result) {
        // 检查是否有有效的美食偏好数据（不是"暂无明确偏好数据"）
        if (!result.taste_preferences && !result.cuisine_preferences) {
            return false;
        }

        // 检查基本口味偏好
        if (result.taste_preferences && result.taste_preferences.basic_tastes) {
            const hasValidTastes = Object.values(result.taste_preferences.basic_tastes)
                .some(value => value !== '暂无明确偏好数据' && value);
            if (hasValidTastes) return true;
        }

        // 检查菜系偏好
        if (result.cuisine_preferences && result.cuisine_preferences.chinese_cuisines) {
            const hasValidCuisines = Object.values(result.cuisine_preferences.chinese_cuisines)
                .some(value => value !== '暂无明确偏好数据' && value);
            if (hasValidCuisines) return true;
        }

        return false;
    }

    updateStepProgress(stepId, progress, message) {
        // 此方法在V2中可能不再需要，因为我们是基于步骤的开始/结束，而不是百分比
        if (this.dynamicSteps.has(stepId)) {
            const stepInfo = this.dynamicSteps.get(stepId);
            stepInfo.progress = progress;
            stepInfo.message = message;
            this.updateDynamicStepsUI();
        }
    }

    formatStepResult(result) {
        // 格式化步骤结果为显示文本
        if (typeof result === 'string') {
            return result;
        } else if (typeof result === 'object' && result !== null) {
            // 根据结果类型进行智能格式化
            if (result.destinations && result.days) {
                // 核心意图分析结果
                const destinations = Array.isArray(result.destinations) ? result.destinations.join('、') : result.destinations;
                const parts = [`${destinations} | ${result.days}天`];

                if (result.travel_theme) parts.push(result.travel_theme);

                return parts.join(' | ');
            } else if (result.attraction_type_preferences) {
                // 景点偏好分析结果 - 新的真实LLM结果格式
                return this.formatAttractionPreferences(result);
            } else if (result.taste_preferences && result.cuisine_preferences) {
                // 美食偏好分析结果 - 新的真实LLM结果格式
                return this.formatFoodPreferences(result);
            } else if (result.accommodation_type_preferences) {
                // 住宿偏好分析结果 - 新的真实LLM结果格式
                return this.formatAccommodationPreferences(result);
            } else if (result.preferred_types) {
                // 旧的景点偏好结果格式（向后兼容）
                return `景点推荐：${result.preferred_types.join('、')}`;
            } else if (result.taste_preferences || result.cuisine_preferences) {
                // 旧的美食偏好结果格式（向后兼容）
                const parts = [];
                if (result.taste_preferences) parts.push(`口味：${Object.keys(result.taste_preferences).join('、')}`);
                if (result.cuisine_preferences) parts.push(`菜系：${Object.keys(result.cuisine_preferences).join('、')}`);
                return parts.join(' | ');
            } else if (result.budget_range || result.special_requirements) {
                // 旧的住宿偏好结果格式（向后兼容）
                const parts = [];
                if (result.budget_range) parts.push(result.budget_range);
                if (result.special_requirements) parts.push(result.special_requirements.join('、'));
                return parts.join(' | ');
            } else {
                // 通用对象格式化
                const keys = Object.keys(result);
                if (keys.length <= 3) {
                    return keys.map(key => `${key}: ${result[key]}`).join(' | ');
                } else {
                    return `包含 ${keys.length} 项分析结果`;
                }
            }
        } else {
            return String(result);
        }
    }

    showStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        const cancelBtn = document.getElementById('cancelPlanningBtn');

        // 重置规划状态，允许用户点击立即规划按钮
        this.isPlanning = false;

        if (startBtn) {
            startBtn.style.display = 'block';
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="bi bi-play-circle"></i> 立即规划';
            startBtn.classList.add('animate__animated', 'animate__fadeInUp');
            startBtn.onclick = () => this.startPlanningPhase();
        }

        if (cancelBtn) {
            cancelBtn.style.display = 'inline-block';
        }

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('分析完成，点击立即规划开始生成行程');
        }
    }

    hideStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.style.display = 'none';
        }
    }

    disableStartPlanningButton() {
        const startBtn = document.getElementById('startPlanningBtn');
        if (startBtn) {
            startBtn.disabled = true;
            startBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 规划中...';
        }
    }
    
    async startItineraryPlanning() {
        try {
            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 隐藏按钮
            document.getElementById('startPlanningBtn').style.display = 'none';

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 启动第二阶段的规划流程
            await this.startPlanningPhase();

        } catch (error) {
            console.error('行程规划失败:', error);
            this.showAlert('行程规划失败: ' + error.message, 'danger');
        }
    }

    async startPlanningPhase() {
        // 检查是否已经在规划中
        if (this.isPlanning) {
            console.log("规划阶段已在进行中，请勿重复点击。");
            return;
        }

        try {
            // 加锁
            this.isPlanning = true;

            // 禁用立即规划按钮
            this.disableStartPlanningButton();

            // 切换到规划阶段
            this.currentPhase = 'planning';
            this.updateUI();

            // 关闭之前的SSE连接
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始生成详细的旅行行程');
            }

            // 准备分析结果数据
            const analysisResult = {
                session_id: this.currentTraceId,
                user_id: this.currentUserId || '1',
                query: this.currentQuery,
                core_intent: this.state.core_intent,
                user_profile: this.state.user_profile,
                user_memories: this.state.user_memories,
                travel_preferences: this.state.travel_preferences,
                preference_profile: this.state.preference_profile,
                driving_context: this.state.driving_context,
                vehicle_info: this.state.vehicle_info,
                multi_city_strategy: this.state.multi_city_strategy
            };

            console.log('启动规划阶段，分析结果:', analysisResult);

            // 使用规划阶段管理器启动规划
            if (window.planningPhaseManager) {
                await window.planningPhaseManager.startPlanningPhase(
                    this.currentTraceId,
                    analysisResult
                );
            } else {
                console.warn('规划阶段管理器未找到，使用降级方案');
                // 降级到原有的SSE连接方式
                await this.startPlanningPhaseFallback();
            }

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showAlert('启动规划失败: ' + error.message, 'danger');

            // 出现错误时解锁
            this.unlockPlanning();
        }
    }

    async startPlanningPhaseFallback() {
        // 原有的SSE连接方式作为降级方案
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim() || '1';

        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=planning`;
        this.eventSource = new EventSource(url);

        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    // 模拟行程生成已移除，现在使用真实的SSE连接
    
    displayItinerary(result) {
        console.log('显示行程数据:', result);

        // 检查数据格式
        if (!result || (!result.itinerary && !result.daily_itineraries)) {
            console.error('行程数据格式错误:', result);
            this.showAlert('行程数据格式错误', 'danger');
            return;
        }

        // 兼容不同的数据格式
        const itineraryData = result.itinerary || result.daily_itineraries || result;
        const summary = result.summary || {};

        // 更新行程标题和描述
        const title = summary.destinations?.join(' → ') || '精彩旅程';
        const description = `${summary.days || itineraryData.length}天${summary.travel_theme || '休闲'}之旅`;

        const titleElement = document.getElementById('itineraryTitle');
        const descElement = document.getElementById('itineraryDescription');

        if (titleElement) titleElement.textContent = title;
        if (descElement) descElement.textContent = description;

        // 更新统计信息
        this.updateItineraryStats(itineraryData, summary);

        // 显示详细行程卡片
        this.displayItineraryCards(itineraryData);

        // 切换到结果视图
        this.currentPhase = 'completed';
        this.updateUI();

        this.currentItinerary = result;
    }

    updateItineraryStats(itineraryData, summary) {
        // 计算统计信息
        const totalDays = itineraryData.length || summary.days || 0;
        let totalPOIs = 0;

        if (Array.isArray(itineraryData)) {
            itineraryData.forEach(day => {
                if (day.time_blocks) {
                    totalPOIs += day.time_blocks.length;
                } else if (day.activities) {
                    totalPOIs += day.activities.length;
                }
            });
        }

        // 更新DOM元素
        const totalDaysElement = document.getElementById('totalDays');
        const totalPOIsElement = document.getElementById('totalPOIs');
        const estimatedBudgetElement = document.getElementById('estimatedBudget');
        const weatherInfoElement = document.getElementById('weatherInfo');

        if (totalDaysElement) totalDaysElement.textContent = totalDays;
        if (totalPOIsElement) totalPOIsElement.textContent = totalPOIs;
        if (estimatedBudgetElement) estimatedBudgetElement.textContent = summary.estimated_budget || '待估算';
        if (weatherInfoElement) weatherInfoElement.textContent = summary.weather || '晴朗';
    }

    displayItineraryCards(itineraryData) {
        console.log('显示行程卡片:', itineraryData);

        if (!Array.isArray(itineraryData)) {
            console.error('行程数据不是数组格式:', itineraryData);
            return;
        }

        // 找到行程容器
        const container = document.querySelector('.itinerary-result') ||
                         document.querySelector('#itineraryView .result-content') ||
                         document.querySelector('#itineraryView');

        if (!container) {
            console.error('未找到行程显示容器');
            return;
        }

        // 生成行程卡片HTML
        const cardsHtml = itineraryData.map((day, index) => {
            return this.generateDayCard(day, index + 1);
        }).join('');

        // 添加操作按钮
        const actionButtonsHtml = `
            <div class="itinerary-actions mt-4">
                <button class="btn btn-primary me-2" onclick="window.app.saveItinerary()">
                    <i class="bi bi-bookmark"></i> 保存行程
                </button>
                <button class="btn btn-outline-primary me-2" onclick="window.app.editItinerary()">
                    <i class="bi bi-pencil"></i> 编辑行程
                </button>
                <button class="btn btn-outline-success" onclick="window.app.shareItinerary()">
                    <i class="bi bi-share"></i> 分享行程
                </button>
            </div>
        `;

        // 更新容器内容
        container.innerHTML = `
            <div class="itinerary-cards">
                ${cardsHtml}
            </div>
            ${actionButtonsHtml}
        `;
    }

    generateDayCard(dayData, dayNumber) {
        // 处理不同的数据格式
        const date = dayData.date || `第${dayNumber}天`;
        const activities = dayData.time_blocks || dayData.activities || [];

        // 生成活动列表
        const activitiesHtml = activities.map(activity => {
            const name = activity.location || activity.name || activity.activity || '未知活动';
            const time = activity.start_time ? `${activity.start_time} - ${activity.end_time}` : '';
            const details = activity.details || activity.description || '';

            return `
                <div class="activity-item mb-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="activity-info">
                            <h6 class="mb-1">${name}</h6>
                            ${details ? `<p class="text-muted small mb-0">${details}</p>` : ''}
                        </div>
                        ${time ? `<span class="badge bg-light text-dark">${time}</span>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="card mb-3 day-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-day me-2"></i>
                        ${date}
                    </h5>
                </div>
                <div class="card-body">
                    ${activitiesHtml || '<p class="text-muted">暂无具体安排</p>'}
                </div>
            </div>
        `;
    }
    
    cancelPlanning() {
        // 停止当前规划
        this.currentPhase = 'waiting';
        this.updateUI();

        // 重置表单
        document.getElementById('userQuery').value = '';

        // 隐藏按钮
        document.getElementById('startPlanningBtn').style.display = 'none';
        document.getElementById('cancelPlanningBtn').style.display = 'none';

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('规划已取消');
        }

        this.showAlert('规划已取消', 'info');
    }

    // 行程操作方法
    saveItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可保存的行程', 'warning');
            return;
        }

        // 这里可以添加保存到本地存储或服务器的逻辑
        localStorage.setItem('saved_itinerary', JSON.stringify(this.currentItinerary));
        this.showAlert('行程已保存到本地', 'success');

        // TTS播报
        if (window.ttsManager) {
            window.ttsManager.speakStatusUpdate('行程已保存');
        }
    }

    editItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可编辑的行程', 'warning');
            return;
        }

        // 这里可以添加编辑行程的逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }

    shareItinerary() {
        if (!this.currentItinerary) {
            this.showAlert('没有可分享的行程', 'warning');
            return;
        }

        // 生成分享链接或文本
        const shareText = `我的旅行计划：${this.currentItinerary.summary?.destinations?.join(' → ') || '精彩旅程'}`;

        if (navigator.share) {
            navigator.share({
                title: '我的旅行计划',
                text: shareText,
                url: window.location.href
            }).then(() => {
                this.showAlert('分享成功', 'success');
            }).catch(() => {
                this.fallbackShare(shareText);
            });
        } else {
            this.fallbackShare(shareText);
        }
    }

    fallbackShare(text) {
        // 降级分享方案：复制到剪贴板
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showAlert('行程信息已复制到剪贴板', 'success');
            }).catch(() => {
                this.showAlert('分享功能暂不可用', 'warning');
            });
        } else {
            this.showAlert('分享功能暂不可用', 'warning');
        }
    }
    
    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const itineraryView = document.getElementById('itineraryView');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'block';
            if (mapView) mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            if (itineraryView) itineraryView.style.display = 'none';
            if (mapView) mapView.style.display = 'block';
        }
    }

    // #############################################################
    // # 以下所有旧的SSE处理逻辑和启动函数都可以被V2版本替代和删除 #
    // #############################################################

    /*
    async startRealPlanning(query, userId) {
        // 生成trace_id
        this.currentTraceId = 'trace_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        // 创建SSE连接 - 只执行分析阶段
        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${userId}&query=${encodeURIComponent(query)}&phase=analysis`;
        this.eventSource = new EventSource(url);

        // 处理SSE事件
        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleSSEEvent(data);
            } catch (error) {
                console.error('解析SSE事件失败:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.eventSource.close();
            this.showAlert('连接服务器失败，请重试', 'danger');
            this.currentPhase = 'waiting';
            this.updateUI();
        };
    }

    handleSSEEvent(data) {
        console.log('收到SSE事件:', data);

        // 支持新旧两种事件格式
        let eventType, payload;

        if (data.event && data.data) {
            // 新格式：符合推送.md文档要求
            eventType = data.event;
            payload = data.data;
            this.handleNewFormatEvent(eventType, payload);
        } else if (data.event_type && data.payload) {
            // 旧格式：保持向后兼容
            eventType = data.event_type;
            payload = data.payload;
            this.handleLegacyFormatEvent(eventType, payload);
        } else {
            console.warn('未知的事件格式:', data);
        }
    }

    handleNewFormatEvent(eventType, payload) {
        console.log('处理新格式事件:', eventType, payload);

        switch (eventType) {
            case 'step_start':
                this.handleStepStart(payload);
                break;
            case 'step_end':
                this.handleStepEnd(payload);
                break;
            case 'step_progress':
                this.handleStepProgress(payload);
                break;
            case 'completed':
                this.handleCompleted(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知的新格式事件类型:', eventType);
        }
    }

    handleLegacyFormatEvent(eventType, payload) {
        console.log('处理旧格式事件:', eventType, payload);

        switch (eventType) {
            case 'start':
                this.handleStart(payload);
                break;
            case 'stream_start':
                this.handleStreamStart(payload);
                break;
            case 'thinking_step':
                this.handleThinkingStep(payload);
                break;
            case 'analysis_step':
                this.handleAnalysisStep(payload);
                break;
            case 'tool_call':
                this.handleToolCall(payload);
                break;
            case 'tool_result':
                this.handleToolResult(payload);
                break;
            case 'itinerary_generated':
                this.handleItineraryGenerated(payload);
                break;
            case 'planning_completed':
                this.handlePlanningCompleted(payload);
                break;
            case 'final_itinerary':
                this.handleFinalItinerary(payload);
                break;
            case 'complete':
                this.handleComplete(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未知的旧格式事件类型:', eventType);
        }
    }

    // ==================== 新格式事件处理方法 ====================

    handleStepStart(payload) {
        console.log('处理步骤开始事件:', payload);

        const stepId = payload.step_id;
        const stepName = payload.step_name;
        const title = payload.title;
        const message = payload.message;

        // 保存步骤信息
        this.dynamicSteps.set(stepId, {
            stepName: stepName,
            title: title,
            status: 'running',
            startTime: payload.timestamp
        });

        // 动态创建UI元素
        this.showStepInProgress(stepId, title, message);
    }

    handleStepEnd(payload) {
        console.log('处理步骤结束事件:', payload);

        const stepId = payload.step_id;
        const stepName = payload.step_name;
        const status = payload.status;
        const result = payload.result;
        const message = payload.message;

        // 更新步骤信息
        if (this.dynamicSteps.has(stepId)) {
            const stepInfo = this.dynamicSteps.get(stepId);
            stepInfo.status = status;
            stepInfo.endTime = payload.timestamp;
            stepInfo.result = result;
        }

        if (status === 'success') {
            this.showStepSuccess(stepId, result);

            // 特殊处理：如果是最终完成事件
            if (stepName === 'completed') {
                this.unlockPlanning(); // 解锁
            }
        } else {
            this.showStepError(stepId, message);
            this.unlockPlanning(); // 出现错误，也要解锁
        }
    }

    handleStepProgress(payload) {
        console.log('处理步骤进度事件:', payload);

        const stepId = payload.step_id;
        const progress = payload.progress;
        const message = payload.message;

        // 更新进度显示
        this.updateStepProgress(stepId, progress, message);
    }

    handleCompleted(payload) {
        console.log('处理完成事件:', payload);

        // 保存最终的状态数据
        if (payload.result) {
            this.updateStateData(payload.result);
        }

        // 如果是分析阶段完成，保存分析结果但不关闭SSE连接
        if (payload.stage === 'analysis' && payload.analysis_result) {
            console.log('保存分析结果:', payload.analysis_result);

            // 将分析结果保存到state中
            Object.assign(this.state, payload.analysis_result);

            console.log('更新后的state:', this.state);

            // 解锁规划按钮，但保持分析状态
            this.unlockForNextPhase();

            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate(payload.message || '分析完成，点击立即规划开始生成行程');
            }

            // 注意：不要在这里关闭SSE连接，因为偏好分析节点还没有执行
            console.log('分析阶段完成，但保持SSE连接以接收偏好分析结果');
            return;
        }

        // 如果是规划阶段完成，才关闭SSE连接
        if (payload.stage === 'planning' || this.currentPhase === 'planning') {
            console.log('规划阶段完成，关闭SSE连接');

            // 显示立即规划按钮（如果还没有显示）
            if (this.currentPhase === 'analysis') {
                this.showStartPlanningButton();
            }

            // 关闭当前的SSE连接
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }
        }
    }

    // ==================== 旧格式事件处理方法 ====================

    handleStart(payload) {
        console.log('处理开始事件:', payload);
        // 可以在这里显示开始分析的状态
    }

    handleStreamStart(payload) {
        console.log('处理流开始事件:', payload);
        // 可以在这里显示流开始的状态
    }

    handleComplete(payload) {
        console.log('处理完成事件:', payload);

        // 保存最终的状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        // 如果是分析阶段完成，保存分析结果
        if (payload.stage === 'analysis' && payload.analysis_result) {
            console.log('保存分析结果:', payload.analysis_result);

            // 将分析结果保存到state中
            Object.assign(this.state, payload.analysis_result);

            // 确保关键字段不为空，并从payload.analysis_result中获取
            if (payload.analysis_result.core_intent) {
                this.state.core_intent = payload.analysis_result.core_intent;
            }
            if (payload.analysis_result.user_profile) {
                this.state.user_profile = payload.analysis_result.user_profile;
            }
            if (payload.analysis_result.user_memories) {
                this.state.user_memories = payload.analysis_result.user_memories;
            }
            if (payload.analysis_result.travel_preferences) {
                this.state.travel_preferences = payload.analysis_result.travel_preferences;
            }
            if (payload.analysis_result.preference_profile) {
                this.state.preference_profile = payload.analysis_result.preference_profile;
            }
            if (payload.analysis_result.driving_context) {
                this.state.driving_context = payload.analysis_result.driving_context;
            }
            if (payload.analysis_result.vehicle_info) {
                this.state.vehicle_info = payload.analysis_result.vehicle_info;
            }
            if (payload.analysis_result.multi_city_strategy) {
                this.state.multi_city_strategy = payload.analysis_result.multi_city_strategy;
            }

            console.log('更新后的state:', this.state);
        }

        // 如果当前是分析阶段，显示立即规划按钮
        if (this.currentPhase === 'analysis') {
            console.log('分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }

        // 关闭当前的SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    handleAnalysisStep(payload) {
        // 处理分析步骤事件
        console.log('处理分析步骤:', payload);

        const stepType = payload.step_type;
        const title = payload.title;
        const content = payload.content;
        const completed = payload.completed;

        // 保存状态数据
        if (payload.data) {
            this.updateStateData(payload.data);
        }

        if (stepType && completed) {
            // 更新对应的分析步骤
            this.completeAnalysisStep(stepType, content);

            // 检查是否所有分析步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                console.log('所有分析步骤完成，显示立即规划按钮');
                this.showStartPlanningButton();
            }
        }
    }

    updateStateData(data) {
        // 更新状态数据
        if (data.core_intent) {
            this.state.core_intent = data.core_intent;
        }
        if (data.user_profile) {
            this.state.user_profile = data.user_profile;
        }
        if (data.user_memories) {
            this.state.user_memories = data.user_memories;
        }
        if (data.travel_preferences) {
            this.state.travel_preferences = data.travel_preferences;
        }
        if (data.preference_profile) {
            this.state.preference_profile = data.preference_profile;
        }
        if (data.driving_context) {
            this.state.driving_context = data.driving_context;
        }
        if (data.vehicle_info) {
            this.state.vehicle_info = data.vehicle_info;
        }
        if (data.multi_city_strategy) {
            this.state.multi_city_strategy = data.multi_city_strategy;
        }

        console.log('状态数据已更新:', this.state);
    }

    handleThinkingStep(payload) {
        // 根据思考步骤更新UI
        console.log('处理思考步骤:', payload);

        // 映射后端的中文分类到前端的步骤
        const categoryMapping = {
            '出行对象': 'user_intent',
            '其他': 'user_intent', // 用户画像分析归类为"其他"，映射到用户需求
            '景点推荐': 'poi_preference',
            '美食推荐': 'food_preference',
            '住宿推荐': 'accommodation_preference'
        };

        const stepKey = categoryMapping[payload.category];
        if (stepKey) {
            this.updateAnalysisStep(stepKey, payload.content);
        } else {
            console.log('未映射的分类:', payload.category, payload.content);
        }

        // 检查是否是分析完成的信号
        if (payload.content && payload.content.includes('分析阶段完成')) {
            console.log('检测到分析阶段完成，显示立即规划按钮');
            this.showStartPlanningButton();
        }
    }

    handleToolCall(payload) {
        console.log('工具调用:', payload.tool_name, payload.parameters);
    }

    handleToolResult(payload) {
        console.log('工具结果:', payload.tool_name, payload.success);
    }

    handleFinalItinerary(payload) {
        // 显示最终行程
        this.displayRealItinerary(payload);
        this.currentPhase = 'completed';
        this.updateUI();

        // 关闭SSE连接
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }

        // TTS播报
        if (window.ttsManager && payload.summary) {
            window.ttsManager.speakItineraryInfo(payload.summary.title, payload.summary.description);
        }
    }

    handleItineraryGenerated(payload) {
        console.log('行程生成中:', payload);

        // 切换到规划阶段视图
        this.currentPhase = 'planning';
        this.updateUI();

        // 更新进度
        const progress = payload.progress || 80;
        this.updatePlanningProgress(progress, '正在生成详细行程...');
    }

    handlePlanningCompleted(payload) {
        console.log('规划完成:', payload);

        // 切换到完成状态
        this.currentPhase = 'completed';
        this.updateUI();

        // 显示完成的行程
        if (payload.summary) {
            this.displayItinerarySummary(payload.summary);
        }
    }

    updatePlanningProgress(progress, message) {
        // 更新规划状态文本
        const title = document.getElementById('analysisStatusTitle');
        const desc = document.getElementById('analysisStatusDesc');

        if (title) title.textContent = message || '正在生成旅行方案...';
        if (desc) desc.textContent = `进度: ${progress}%`;
    }

    displayItinerarySummary(summary) {
        console.log('显示行程摘要:', summary);
        // 这里可以添加显示行程摘要的逻辑
        // 暂时显示一个简单的提示
        this.showAlert('行程规划完成！', 'success');
    }

    handleError(payload) {
        console.error('规划错误:', payload);
        this.showAlert('规划过程中发生错误: ' + payload.error_message, 'danger');
        this.currentPhase = 'waiting';
        this.updateUI();

        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateAnalysisStep(stepKey, content) {
        const stepElement = document.querySelector(`[data-step="${stepKey}"]`);
        if (stepElement) {
            // 标记为活跃状态
            stepElement.classList.add('active');

            // 更新结果内容
            const resultElement = stepElement.querySelector('.analysis-result');
            if (resultElement) {
                resultElement.innerHTML = content;
            }

            // 更新状态为完成
            const statusElement = stepElement.querySelector('.analysis-status');
            if (statusElement) {
                statusElement.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }

            // 标记为完成
            stepElement.classList.add('completed');
            this.analysisSteps[stepKey].completed = true;

            // 检查是否所有分析步骤都完成了
            const allStepsCompleted = ['user_intent', 'poi_preference', 'food_preference', 'accommodation_preference']
                .every(key => this.analysisSteps[key].completed);

            if (allStepsCompleted) {
                // 显示用户画像
                this.showUserProfile();
            }

            // 检查是否所有步骤都完成了
            const allCompleted = Object.values(this.analysisSteps).every(step => step.completed);
            if (allCompleted) {
                this.showStartPlanningButton();

                // 所有分析步骤完成后，关闭SSE连接
                console.log('所有分析步骤完成，关闭SSE连接');
                if (this.eventSource) {
                    this.eventSource.close();
                    this.eventSource = null;
                }
            }
        }
    }

    showUserProfile() {
        console.log('showUserProfile方法被调用');
        // 用户画像数据已经通过分析步骤显示在四个分析框中
        // 不需要额外的userProfileItem元素
        console.log('用户画像数据已通过分析步骤显示');

        // 从后端获取用户画像数据（如果需要）
        this.fetchUserProfile();
    }

    async fetchUserProfile() {
        console.log('fetchUserProfile方法被调用');
        try {
            console.log('开始获取用户画像数据');
            const response = await fetch('/api/travel/user_profile', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            console.log('API响应状态:', response.status);
            if (response.ok) {
                const userProfile = await response.json();
                console.log('获取到用户画像数据:', userProfile);
                this.displayUserProfile(userProfile);
            } else {
                console.error('获取用户画像失败:', response.statusText);
                // 使用模拟数据作为后备
                this.displayUserProfile(this.getMockUserProfile());
            }
        } catch (error) {
            console.error('获取用户画像出错:', error);
            // 使用模拟数据作为后备
            this.displayUserProfile(this.getMockUserProfile());
        }
    }

    getMockUserProfile() {
        return {
            basic_info: {
                age: "25-35岁",
                gender: "女性",
                occupation: "白领",
                travel_companion: "亲子"
            },
            tags: ["慢节奏", "趣味性", "儿童友好", "舒适型"],
            budget_preference: "舒适型",
            preferences: {
                travel_style: "深度游",
                season: "春季",
                duration: "3天"
            },
            recommendation_reason: "根据您的亲子出行需求和舒适型预算偏好，我们为您推荐了适合家庭的景点和活动，确保既有趣味性又能让孩子们开心游玩。"
        };
    }

    displayUserProfile(userProfile) {
        console.log('displayUserProfile方法被调用，数据:', userProfile);

        // 更新基本信息
        const userBasicInfo = document.getElementById('userBasicInfo');
        if (userBasicInfo && userProfile.basic_info) {
            const basicInfo = userProfile.basic_info;
            const infoText = [
                basicInfo.age,
                basicInfo.gender,
                basicInfo.occupation,
                basicInfo.travel_companion
            ].filter(Boolean).join('，');
            userBasicInfo.textContent = infoText || '暂无信息';
        }

        // 更新旅行风格
        const userTravelStyle = document.getElementById('userTravelStyle');
        if (userTravelStyle) {
            userTravelStyle.textContent = userProfile.preferences?.travel_style || userProfile.travel_style || '休闲';
        }

        // 更新兴趣标签
        const userTags = document.getElementById('userTags');
        if (userTags && userProfile.tags) {
            userTags.textContent = userProfile.tags.join('，') || '暂无标签';
        }

        // 更新偏好设置
        const userPreferences = document.getElementById('userPreferences');
        if (userPreferences && userProfile.preferences) {
            const prefs = [];
            if (userProfile.preferences.accommodation_preferences) {
                prefs.push(`住宿：${userProfile.preferences.accommodation_preferences.join('，')}`);
            }
            if (userProfile.preferences.transportation_preferences) {
                prefs.push(`交通：${userProfile.preferences.transportation_preferences.join('，')}`);
            }
            userPreferences.textContent = prefs.join('；') || '暂无特殊偏好';
        }

        // 更新预算偏好
        const userBudget = document.getElementById('userBudget');
        if (userBudget) {
            userBudget.textContent = userProfile.budget_preference || '中等';
        }

        console.log('用户画像显示完成');
    }

    displayBasicInfo(basicInfo) {
        const basicInfoTags = document.getElementById('basicInfoTags');
        if (basicInfoTags && basicInfo) {
            const tags = [];
            if (basicInfo.age) tags.push(basicInfo.age);
            if (basicInfo.gender) tags.push(basicInfo.gender);
            if (basicInfo.occupation) tags.push(basicInfo.occupation);
            if (basicInfo.travel_companion) tags.push(basicInfo.travel_companion);

            basicInfoTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag basic-info">${tag}</span>`
            ).join('');
        }
    }

    displayPreferenceTags(tags) {
        const preferencesTags = document.getElementById('preferencesTags');
        if (preferencesTags && tags.length > 0) {
            preferencesTags.innerHTML = tags.map(tag =>
                `<span class="profile-tag">${tag}</span>`
            ).join('');
        }
    }

    displayBudgetPreference(budgetPreference) {
        const budgetTags = document.getElementById('budgetTags');
        if (budgetTags && budgetPreference) {
            budgetTags.innerHTML = `<span class="profile-tag budget">${budgetPreference}</span>`;
        }
    }

    displayRecommendationReason(reason) {
        const recommendationText = document.getElementById('recommendationText');
        if (recommendationText && reason) {
            recommendationText.textContent = reason;
        }
    }

    displayRealItinerary(itineraryData) {
        // 从真实数据中提取信息
        const summary = itineraryData.summary || {};
        const dailyPlans = itineraryData.daily_plans || [];
        const budgetEstimation = itineraryData.budget_estimation || {};

        // 更新行程标题和描述
        document.getElementById('itineraryTitle').textContent = summary.title || '旅行行程';
        document.getElementById('itineraryDescription').textContent = summary.description || '个性化旅行方案';

        // 更新统计信息
        document.getElementById('totalDays').textContent = summary.days || dailyPlans.length;
        document.getElementById('totalPOIs').textContent = this.countTotalPOIs(dailyPlans);

        // 格式化预算信息
        const budgetText = this.formatBudget(budgetEstimation);
        document.getElementById('estimatedBudget').textContent = budgetText;

        // 更新天气信息
        const weatherInfo = this.extractWeatherInfo(itineraryData);
        document.getElementById('weatherInfo').textContent = weatherInfo;

        this.currentItinerary = itineraryData;
    }

    countTotalPOIs(dailyPlans) {
        let total = 0;
        dailyPlans.forEach(day => {
            if (day.pois) {
                total += day.pois.length;
            }
        });
        return total;
    }

    formatBudget(budgetEstimation) {
        if (budgetEstimation.total_min && budgetEstimation.total_max) {
            return `¥${budgetEstimation.total_min}-${budgetEstimation.total_max}`;
        } else if (budgetEstimation.total_min) {
            return `¥${budgetEstimation.total_min}+`;
        }
        return '待计算';
    }

    extractWeatherInfo(itineraryData) {
        // 从工具结果中提取天气信息
        if (itineraryData.weather_forecast && itineraryData.weather_forecast.length > 0) {
            const weather = itineraryData.weather_forecast[0];
            return weather.weather || weather.dayweather || '晴';
        }
        return '晴';
    }

    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面
        document.body.appendChild(alertDiv);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
            
            // TTS播报
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('行程已保存');
            }
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }
    
    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }
    
    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.title,
                text: this.currentItinerary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }
    
    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }
    
    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }
    */

}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TravelPlannerAppRefactored();
    window.travelPlannerApp = window.app; // 为PlanningPhaseManager提供引用
});
