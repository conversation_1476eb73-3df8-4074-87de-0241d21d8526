import asyncio
import pytest
import json
from typing import List, Dict, Any

print("--- [Debug] Loading test_v2_event_flow.py ---")

from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.services.notification_service import NotificationService
from src.database.redis_client import RedisClient

print("--- [Debug] Imports successful ---")

# Pytest-asyncio decorator
@pytest.mark.asyncio
async def test_v2_end_to_end_event_flow():
    """
    [V2] 端到端事件流集成测试

    本测试验证从Agent启动到事件通过Redis Pub/Sub发布的完整后台流程。
    它不涉及API层或前端，专注于核心后端逻辑的正确性。

    测试步骤:
    1. 初始化Agent, RedisClient, 和 NotificationService。
    2. 创建一个Redis订阅者，监听特定的测试任务频道。
    3. 在后台启动Agent的规划任务 (`run_planning_with_notifications`)。
    4. 主程序作为订阅者，接收并验证从Redis发布的事件流。
    5. 验证事件的顺序和基本结构是否符合预期。
    """
    print("\n--- [Debug] Entering test_v2_end_to_end_event_flow ---")
    # 1. 初始化
    test_task_id = "test_v2_flow_task_12345"
    test_user_id = "test_user_v2"
    test_query = "周末去上海玩两天"

    # 假设测试环境可以访问到Redis
    redis_client = RedisClient()
    await redis_client.connect()
    
    notification_service = NotificationService(redis_client)
    agent = TravelPlannerAgentLangGraph()

    # 用于存储接收到的事件
    received_events: List[Dict[str, Any]] = []

    # 2. 创建Redis订阅者
    async def redis_subscriber():
        channel = f"task:{test_task_id}"
        pubsub = redis_client.client.pubsub()
        await pubsub.subscribe(channel)
        
        print(f"\n[Subscriber] 正在监听频道: {channel}")
        
        # 设置一个超时，以防测试永远挂起
        listen_timeout = 60  # seconds
        start_time = asyncio.get_event_loop().time()

        while asyncio.get_event_loop().time() - start_time < listen_timeout:
            try:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=1.0)
                if message:
                    event_data = json.loads(message['data'].decode('utf-8'))
                    print(f"[Subscriber] 收到事件: {event_data.get('event')}")
                    received_events.append(event_data)
                    # 如果收到eos信号，可以提前退出
                    if event_data.get("event") == "eos":
                        break
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                print(f"[Subscriber] 监听时发生错误: {e}")
                break
        
        await pubsub.unsubscribe(channel)
        print("[Subscriber] 停止监听。")

    # 3. 在后台启动Agent
    agent_task = asyncio.create_task(
        agent.run_planning_with_notifications(
            task_id=test_task_id,
            user_id=test_user_id,
            query=test_query,
            notification_service=notification_service
        )
    )

    # 4. 运行订阅者来收集事件
    await redis_subscriber()

    # 等待Agent任务完成（尽管它可能因为错误已经结束）
    # 我们给它一点时间来完成清理
    try:
        await asyncio.wait_for(agent_task, timeout=5.0)
    except asyncio.TimeoutError:
        print("[Test Runner] Agent任务在超时后仍在运行，可能已挂起。继续进行断言。")
    except Exception as e:
        print(f"[Test Runner] Agent任务抛出未捕获的异常: {e}")


    # 5. 断言 - 验证事件流
    print(f"\n[Assertions] 共收到 {len(received_events)} 个事件。开始验证...")
    
    assert len(received_events) > 2, "应至少收到开始和结束/错误事件"

    # 验证第一个事件是step_start
    first_event = received_events[0]
    assert first_event['event'] == 'step_start', "第一个事件应该是 'step_start'"
    assert 'data' in first_event
    assert 'step_id' in first_event['data']

    # 验证最后一个事件是eos
    last_event = received_events[-1]
    assert last_event['event'] == 'eos', "最后一个事件应该是 'eos'"

    # 验证是否存在 'complete' 或 'error' 事件
    has_completion_or_error = any(e['event'] in ['complete', 'error'] for e in received_events)
    assert has_completion_or_error, "事件流中应包含 'complete' 或 'error' 事件"

    # 验证事件顺序
    eos_index = -1
    for i, event in enumerate(received_events):
        if event['event'] == 'eos':
            eos_index = i
            break
    
    assert eos_index == len(received_events) - 1, "'eos' 事件必须是最后一个"

    print("[Assertions] 事件流基本结构和顺序验证通过！")

    # 清理
    await redis_client.disconnect() 