<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoPilot AI - 智能旅行规划</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style-refactored.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-airplane"></i>
                AutoPilot AI
            </a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-warning btn-sm me-2" id="v2TestBtn">
                    <i class="bi bi-lightning"></i>
                    V2测试
                </button>
                <button class="btn btn-outline-light btn-sm me-2" id="ttsToggle">
                    <i class="bi bi-volume-up"></i>
                    语音播报
                </button>
                <button class="btn btn-outline-light btn-sm" id="historyBtn">
                    <i class="bi bi-clock-history"></i>
                    历史行程
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid p-0">
        <!-- 用户查询输入区域 -->
        <div class="query-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="query-input-card">
                            <form id="planningForm">
                                <div class="input-group">
                                    <textarea
                                        class="form-control query-input"
                                        id="userQuery"
                                        rows="2"
                                        placeholder="告诉我你的旅行想法，例如：我想从亦庄出发去北京市区玩2天，主要想看历史文化景点..."
                                        required
                                    >我在福州闽东大厦，这周末要去莆田玩两天</textarea>
                                    <button type="submit" class="btn btn-primary query-submit" id="planButton">
                                        <i class="bi bi-send"></i>
                                        开始规划
                                    </button>
                                </div>
                                <input type="hidden" id="userId" value="1">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 双栏布局主体 -->
        <div class="main-content">
            <div class="container-fluid">
                <div class="row g-0">
                    <!-- 左栏：分析面板 -->
                    <div class="col-lg-5 analysis-panel">
                        <div class="panel-header">
                            <h5 class="panel-title">
                                <i class="bi bi-cpu"></i>
                                AI分析过程
                            </h5>
                            <button class="btn btn-sm btn-outline-secondary" id="ttsToggle">
                                <i class="bi bi-volume-up"></i>
                                语音播报
                            </button>
                        </div>

                        <div class="panel-body">
                            <!-- 动态分析步骤容器 - 由后端SSE事件动态创建 -->
                            <div id="analysisSteps" class="analysis-steps">
                                <!-- 分析步骤将由后端step_start事件动态创建 -->
                                <div class="analysis-placeholder">
                                    <div class="text-center text-muted">
                                        <i class="bi bi-clock-history"></i>
                                        <p class="mt-2">等待开始分析...</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 控制按钮 -->
                            <div class="analysis-controls mt-4">
                                <button class="btn btn-success w-100" id="startPlanningBtn" style="display: none;">
                                    <i class="bi bi-play-circle"></i>
                                    立即规划
                                </button>
                                <button class="btn btn-outline-danger" id="cancelPlanningBtn" style="display: none;">
                                    <i class="bi bi-x-circle"></i>
                                    取消生成
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 右栏：行程与状态面板 -->
                    <div class="col-lg-7 itinerary-panel">
                        <div class="panel-header">
                            <h5 class="panel-title">
                                <i class="bi bi-map"></i>
                                旅行规划结果
                            </h5>
                            <div class="panel-controls">
                                <button class="btn btn-sm btn-outline-primary" id="viewModeList">
                                    <i class="bi bi-list-ul"></i>
                                    列表
                                </button>
                                <button class="btn btn-sm btn-outline-primary" id="viewModeMap">
                                    <i class="bi bi-geo-alt"></i>
                                    地图
                                </button>
                            </div>
                        </div>

                        <div class="panel-body">
                            <!-- 阶段一：等待状态 -->
                            <div id="waitingView" class="status-view">
                                <div class="status-content">
                                    <i class="bi bi-compass status-icon"></i>
                                    <h4 class="status-title">开始你的智能旅行规划</h4>
                                    <p class="status-description">输入你的旅行想法，AI将为你生成个性化的旅行行程</p>
                                </div>
                            </div>

                            <!-- 阶段一：分析状态 -->
                            <div id="analysisView" class="status-view" style="display: none;">
                                <div class="status-content">
                                    <div class="status-spinner">
                                        <div class="spinner-border text-primary" role="status"></div>
                                    </div>
                                    <h4 class="status-title" id="analysisStatusTitle">正在分析您的需求...</h4>
                                    <p class="status-description" id="analysisStatusDesc">AI正在理解您的旅行偏好和需求</p>
                                </div>
                            </div>

                            <!-- 阶段二：流式行程构建 -->
                            <div id="itineraryView" class="itinerary-content" style="display: none;">
                                <!-- 行程头部信息 -->
                                <div class="itinerary-header">
                                    <div class="itinerary-title-section">
                                        <h3 id="itineraryTitle" class="itinerary-title">行程标题</h3>
                                        <p id="itineraryDescription" class="itinerary-description">行程描述</p>
                                    </div>
                                    <div class="itinerary-actions">
                                        <button class="btn btn-outline-success btn-sm" id="saveItinerary">
                                            <i class="bi bi-save"></i>
                                            保存
                                        </button>
                                        <button class="btn btn-outline-primary btn-sm" id="editItinerary">
                                            <i class="bi bi-pencil"></i>
                                            编辑
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" id="shareItinerary">
                                            <i class="bi bi-share"></i>
                                            分享
                                        </button>
                                    </div>
                                </div>

                                <!-- 行程统计卡片 -->
                                <div class="itinerary-stats">
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-calendar-event"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="totalDays">0</div>
                                            <div class="stat-label">天数</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-geo-alt"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="totalPOIs">0</div>
                                            <div class="stat-label">景点</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-currency-dollar"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="estimatedBudget">¥0</div>
                                            <div class="stat-label">预算</div>
                                        </div>
                                    </div>
                                    <div class="stat-card">
                                        <div class="stat-icon">
                                            <i class="bi bi-cloud-sun"></i>
                                        </div>
                                        <div class="stat-content">
                                            <div class="stat-value" id="weatherInfo">晴</div>
                                            <div class="stat-label">天气</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 每日行程卡片 -->
                                <div id="dailyItinerary" class="daily-itinerary">
                                    <!-- 每日行程卡片将在这里流式添加 -->
                                </div>
                            </div>

                            <!-- 地图视图 -->
                            <div id="mapView" class="map-view" style="display: none;">
                                <div id="mapContainer" class="map-container">
                                    <div class="map-placeholder">
                                        <i class="bi bi-map display-4 text-muted"></i>
                                        <p class="text-muted mt-2">地图功能开发中...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史行程模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history"></i>
                        历史行程
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="historyList" class="history-list">
                        <!-- 历史行程列表 -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在加载历史行程...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- TTS播报状态指示器 -->
    <div id="ttsIndicator" class="tts-indicator" style="display: none;">
        <div class="tts-content">
            <i class="bi bi-volume-up tts-icon"></i>
            <span class="tts-text">正在播报...</span>
            <button class="btn btn-sm btn-outline-light" id="stopTTS">
                <i class="bi bi-stop"></i>
            </button>
        </div>
    </div>

    <!-- 全局加载遮罩 -->
    <div id="globalLoading" class="global-loading" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-border text-primary" role="status"></div>
            </div>
            <h5 class="loading-title">AI正在为您规划行程</h5>
            <p class="loading-description">请稍候，这可能需要几分钟时间...</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/tts.js"></script>
    <script src="/static/js/planning-phase.js?v=1751989000"></script>
    <script src="/static/js/app-refactored.js?v=1751989000"></script>
</body>
</html>
