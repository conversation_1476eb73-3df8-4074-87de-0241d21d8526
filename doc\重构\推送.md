# Agent实时推送架构重构方案 (V2.0)

本文档旨在提出一套基于**事件驱动**和**责任分离**原则的全新实时推送架构，以从根本上解决现有推送机制的耦合问题，并大幅提升系统的健壮性、可维护性和可扩展性。

## 一、问题分析：从表面到根源

### 1. 表面问题
- **SSE推送延迟**：在IO密集型操作（如数据库查询、多工具调用）中，开始和结束事件被一同缓冲，导致前端长时间“假死”。
- **前端体验不佳**：用户无法获得实时的进度反馈，可能因等待时间过长而重复提交请求。

### 2. 架构根源
现有方案（`SSEStreamAdapter`）的核心问题在于**责任不清**和**高度耦合**：

- **业务逻辑泄露 (`Business Logic Leakage`)**: `SSEStreamAdapter`被迫承担了业务逻辑判断的责任。它需要通过检查LangGraph `state`中的字段（如`if "core_intent" in state:`）来“猜测”业务走到了哪一步。这违反了单一职责原则。
- **高度耦合 (`High Coupling`)**: 推送逻辑与LangGraph的节点名称、状态结构强绑定。一旦业务流程（`nodes.py`）发生变化（如增删改节点），`SSEStreamAdapter`必须同步修改，维护成本极高，且容易出错。
- **可复用性差 (`Poor Reusability`)**: 如果要开发新的Agent，当前的`SSEStreamAdapter`几乎无法复用，因为其中的逻辑是为`TravelPlannerAgent`量身定做的。

## 二、新架构蓝图：基于Redis Pub/Sub的事件驱动模型

我们提出一个全新的事件驱动架构，将业务逻辑与通知机制彻底解耦。

### 1. 核心原则
- **责任分离**：业务节点只负责执行业务并“宣告”事件。通知服务负责将事件发布到消息总线。API层负责从总线订阅事件并推送给客户端。
- **事件驱动**：组件之间通过消息（事件）进行通信，而不是直接调用。这大大降低了组件间的依赖。

### 2. 核心组件
- **`NotificationService` (事件发布者)**: 一个全新的服务，负责将业务事件发布到Redis的Pub/Sub频道。它**只与Redis交互**。
- **LangGraph Nodes (业务逻辑单元)**: 在执行关键业务逻辑前后，调用`NotificationService`来宣告“我开始了”或“我结束了”。
- **Redis Pub/Sub (消息总线)**: 作为解耦的中间件，传递实时事件。
- **API Endpoint (事件订阅与推送者)**: API流式接口订阅Redis频道，接收事件，并将其格式化为SSE推送给前端。

### 3. 数据流转图

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant API as API Endpoint
    participant BG as LangGraph (Background Task)
    participant NS as NotificationService
    participant Redis as Redis Pub/Sub

    FE->>+API: 发起流式规划请求 (e.g., /plan_stream)
    API->>BG: 启动后台规划任务 (task_id)
    API->>Redis: 订阅频道 (SUBSCRIBE channel:{task_id})
    API-->>-FE: 建立SSE长连接

    loop 规划流程
        BG->>NS: 调用 notify_step_start()
        NS->>Redis: 发布事件 (PUBLISH channel:{task_id})
        Redis-->>API: 收到开始事件
        API-->>FE: 推送SSE step_start 事件

        BG->>NS: 调用 notify_step_end()
        NS->>Redis: 发布事件 (PUBLISH channel:{task_id})
        Redis-->>API: 收到结束事件
        API-->>FE: 推送SSE step_end 事件
    end
```

## 三、核心代码实现

### 1. `NotificationService` (`src/services/notification_service.py`)

这个服务是新架构的核心，负责所有事件的发布。

```python
# src/services/notification_service.py
import json
import logging
from typing import Dict, Any
from src.database.redis_client import RedisClient # 假设RedisClient已初始化

logger = logging.getLogger(__name__)

class NotificationService:
    def __init__(self, redis_client: RedisClient):
        self.redis = redis_client.client

    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        channel = f"task:{task_id}"
        message = json.dumps(event_data, ensure_ascii=False)
        await self.redis.publish(channel, message)
        logger.debug(f"Published to {channel}: {message}")

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        event = {
            "event": "step_start",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "title": title,
                "message": message
            }
        }
        await self._publish(task_id, event)

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        event = {
            "event": "step_end",
            "data": {
                "step_id": f"{step_name}_{task_id}",
                "step_name": step_name,
                "status": status,
                "result": result or {}
            }
        }
        await self._publish(task_id, event)

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        event = {
            "event": "complete",
            "data": final_data
        }
        await self._publish(task_id, event)
        # 发送一个特殊消息来关闭连接
        await self._publish(task_id, {"event": "eos"}) # End of Stream

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        event = {
            "event": "error",
            "data": {
                "step_name": step_name,
                "message": error_message
            }
        }
        await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos"})

**重要提示**: 此服务的设计**完全复用**了项目现有的Redis基础设施 (`src/database/redis_client.py`)。它通过依赖注入接收`RedisClient`实例，确保了连接池的统一管理和代码的一致性，避免了重复造轮子。

### 2. 改造LangGraph节点 (`nodes.py`)

业务节点现在显式调用通知服务，代码即文档。

```python
# src/agents/travel_planner_langgraph/nodes.py

# notification_service 将通过依赖注入传入
async def core_intent_analyzer(state: TravelPlanState, notification_service: NotificationService):
    task_id = state["session_id"]
    await notification_service.notify_step_start(
        task_id=task_id,
        step_name="core_intent_analysis",
        title="解析用户需求",
        message="正在分析您的旅行意图..."
    )
    
    try:
        # ... 执行LLM调用等核心业务逻辑 ...
        result = await llm_service.analyze_intent(...)
        
        await notification_service.notify_step_end(
            task_id=task_id,
            step_name="core_intent_analysis",
            status="success",
            result=result
        )
        return {"core_intent": result}
        
    except Exception as e:
        await notification_service.notify_error(task_id, str(e), "core_intent_analysis")
        raise
```

### 3. 改造API流式接口 (`travel_planner.py`)

这是将Redis Pub/Sub事件流转换为SSE的关键。

```python
# src/api/travel_planner.py
import asyncio
import json
from fastapi import APIRouter, Depends, Request, BackgroundTasks
from sse_starlette.sse import EventSourceResponse
from src.database.redis_client import get_redis_client # 依赖注入
from src.services.notification_service import NotificationService
from src.agents import TravelPlannerAgentLangGraph # Agent实例

router = APIRouter()

async def redis_event_generator(task_id: str, redis_client):
    """监听Redis频道并生成事件"""
    channel = f"task:{task_id}"
    pubsub = redis_client.pubsub()
    await pubsub.subscribe(channel)
    
    try:
        while True:
            message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=60)
            if message:
                data = message['data'].decode('utf-8')
                event_data = json.loads(data)
                
                if event_data.get("event") == "eos": # 检查结束信号
                    break
                
                yield f"data: {data}\n\n"
            await asyncio.sleep(0.1)
    finally:
        await pubsub.unsubscribe(channel)


@router.post("/v2/plan/stream")
async def plan_travel_stream_v2(
    request_data: dict, # 包含 user_id, query 等
    background_tasks: BackgroundTasks,
    redis_client = Depends(get_redis_client)
):
    """V2版流式规划接口"""
    user_id = request_data.get("user_id")
    query = request_data.get("query")
    task_id = f"task_{user_id}_{int(time.time())}"

    # 初始化Agent和Service
    agent = TravelPlannerAgentLangGraph()
    notification_service = NotificationService(redis_client)

    # 将Agent的执行放入后台任务
    background_tasks.add_task(
        agent.run_planning_with_notifications, # Agent需要一个新方法
        task_id=task_id,
        user_id=user_id,
        query=query,
        notification_service=notification_service
    )
    
    # 立即返回SSE响应，并开始监听Redis
    return EventSourceResponse(redis_event_generator(task_id, redis_client))

```

## 四、实施路径图

为确保重构工作清晰、可执行，以下步骤明确了需要修改或创建的关键文件路径：

1.  **创建 `NotificationService`**:
    -   **位置**: `src/services/notification_service.py` (新建)
    -   **任务**: 实现文档第三部分描述的`NotificationService`类。
    -   **关键依赖**: 此服务必须完全复用位于 `src/database/redis_client.py` 的现有`RedisClient`，通过依赖注入方式获取连接。

2.  **改造Agent协调器**:
    -   **位置**: `src/agents/travel_planner_agent_langgraph.py` (修改)
    -   **任务**: 添加新方法 `run_planning_with_notifications`，该方法接收`notification_service`实例并将其通过`config`注入到Graph的执行流程中。

3.  **改造所有Node**:
    -   **位置**: `src/agents/travel_planner_langgraph/nodes.py` (修改)
    -   **任务**: 逐一修改文件中的所有节点函数，在关键业务逻辑前后添加对`notification_service`的调用，以发布`step_start`和`step_end`事件。

4.  **实现新API端点**:
    -   **位置**: `src/api/travel_planner.py` (修改)
    -   **任务**: 添加新的`/v2/plan/stream`端点，实现后台任务启动和对Redis Pub/Sub的监听逻辑。

5.  **前端适配**:
    -   **位置**: `static/js/app-refactored.js` (修改)
    -   **任务**: 移除旧的轮询逻辑，修改SSE事件处理函数`handleSSEEvent`以适配V2.0的新事件格式。UI元素的更新应基于查找现有HTML元素并修改其状态，而非动态创建。

6.  **废弃 `SSEStreamAdapter`**:
    -   **位置**: `src/agents/travel_planner_langgraph/stream_adapter.py` (删除)
    -   **任务**: 一旦新流程在测试环境中验证通过，可安全删除此文件及其在 `travel_planner_agent_langgraph.py` 中的所有调用。


## 五、新架构优势

- **完全解耦**: 业务逻辑与推送机制分离，各自独立演进。
- **高可维护性**: 修改业务流程只需关心Node本身，无需触碰任何推送代码。
- **实时性保障**: 事件通过Redis Pub/Sub实时广播，消除了IO阻塞导致的延迟。
- **高可扩展性**: 可以轻松添加新的事件订阅者，如日志系统、监控面板，而无需修改Agent。
- **状态持久化**: 虽然本方案核心是Pub/Sub，但`NotificationService`可以轻松扩展，将事件也存入Redis Hash或Stream中，实现任务状态的持久化记录和恢复。
- **代码清晰**: 业务流程在Node中一目了然，代码即文档。

## 六、前端适配指南 (V2.0)

新后端架构的实施，将使前端的事件处理和状态管理逻辑**得到大幅简化**。原有的“SSE+轮询”双轨制可以被彻底废弃，转向更优雅、更可靠的纯SSE事件驱动模型。

### 1. 核心原则：单一数据源与逻辑简化

- **移除轮询 (`Polling`)**: 后端所有进度更新都将通过SSE实时推送。前端不再需要、也不应该通过`setInterval`去轮询任何进度API。这会移除大量复杂的异步和状态管理代码。
- **SSE作为唯一真实来源 (`Single Source of Truth`)**: 所有与任务状态相关的UI更新，都应由接收到的SSE事件驱动。

### 2. SSE事件处理改造 (`app-refactored.js`)

前端需要修改其核心的SSE事件处理函数，以适应新的事件格式并移除轮询逻辑。

#### a. 修改前 (可能存在的轮询逻辑)
```javascript
// 修改前的伪代码 - 包含轮询
class TravelPlannerApp {
    // ...
startPlanning() {
        if (this.isPlanning) return;
        this.isPlanning = true;
        this.connectToSSE(); // 建立SSE连接
        this.startProgressPolling(); // 启动轮询
    }

    startProgressPolling() {
        this.progressTimer = setInterval(async () => {
            // ... fetch an API to get progress ...
        }, 1000);
    }
    
    finishPlanning() {
        this.isPlanning = false;
        clearInterval(this.progressTimer); // 清除轮询
    }
    // ...
}
```

#### b. 修改后 (纯SSE事件驱动)
```javascript
// 修改后的伪代码 - 纯SSE驱动
class TravelPlannerApp {
    // ...
    constructor() {
        this.isPlanning = false;
        this.eventSource = null; // 用于管理SSE连接
    }

    startPlanning() {
        if (this.isPlanning) {
            console.log("规划任务已在进行中...");
            return;
        }
        this.isPlanning = true;
        document.getElementById('plan-button').disabled = true;

        this.connectToSSE(); // 唯一的入口
    }

    connectToSSE() {
        // 假设API端点为 /v2/plan/stream
        this.eventSource = new EventSource('/api/travel/v2/plan/stream'); // 使用新API

        this.eventSource.onmessage = (event) => {
            const eventData = JSON.parse(event.data);
            this.handleSSEEvent(eventData);
        };

        this.eventSource.onerror = (error) => {
            console.error("SSE Connection Error:", error);
            this.showError("与服务器的实时连接断开，请刷新重试。");
            this.finishPlanning(); // 出错时也要解锁
        };
    }

    handleSSEEvent(eventData) {
        // V2.0 新的事件结构
        const eventType = eventData.event;
        const data = eventData.data;

        switch (eventType) {
    case 'step_start':
                this.showStepInProgress(data.step_id, data.title, data.message);
      break;
      
    case 'step_end':
                this.showStepSuccess(data.step_id, data.status, data.result);
                break;

            case 'complete':
                console.log("规划完成!", data);
                this.renderFinalItinerary(data);
                // 注意：不再在这里解锁，等待'eos'事件
                break;

            case 'error':
                console.error("规划出错:", data.message);
                this.showError(data.message);
                // 注意：不再在这里解锁，等待'eos'事件
                break;
            
            case 'eos': // End of Stream
                console.log("收到流结束信号，关闭连接。");
                this.finishPlanning();
                break;
        }
    }

    finishPlanning() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isPlanning = false;
        document.getElementById('plan-button').disabled = false;
      }
    // ...
}
```

### 3. UI状态更新 (基于现有HTML结构)

**重要前提**: 此方案假定前端页面已存在用于展示各个规划步骤的静态HTML元素。前端JS的职责是**找到**这些元素并**更新它们的状态**（如文本、样式），而不是动态创建它们。这确保了UI视觉呈现保持不变。

```javascript
// `app-refactored.js` 中修改后的UI更新函数

/**
 * 当一个新步骤开始时，更新其UI元素为“进行中”状态
 * @param {string} stepId - 步骤的唯一ID (e.g., "core_intent_analysis_task_...")
 * @param {string} title -  步骤的标题 (e.g., "解析用户需求")
 * @param {string} message - 当前状态消息 (e.g., "正在分析您的旅行意图...")
 */
showStepInProgress(stepId, title, message) {
    // 假设每个步骤卡片都有一个固定的ID，例如 id="step-core_intent_analysis"
    // 注意：step_id 可能会包含 task_id 后缀，前端处理时可能需要截取固定的部分
    const stepCardId = `step-${stepId.split('_')[0]}`;
    const stepCard = document.getElementById(stepCardId); 
    if (!stepCard) {
        console.warn(`未找到步骤卡片: ${stepCardId}`);
        return;
    }

    const titleElement = stepCard.querySelector('.step-title'); // 假设标题元素有.step-title class
    const messageElement = stepCard.querySelector('.step-message'); // 假设消息元素有.step-message class
    
    stepCard.classList.remove('success', 'error', 'hidden');
    stepCard.classList.add('in-progress');
    
    if (titleElement) titleElement.textContent = title;
    if (messageElement) messageElement.textContent = message;
}

/**
 * 当一个步骤成功结束时，更新其UI为“已完成”状态
 * @param {string} stepId - 步骤的唯一ID
 * @param {string} status - 'success'
 * @param {object} result - 步骤的产出结果
 */
showStepSuccess(stepId, status, result) {
    const stepCardId = `step-${stepId.split('_')[0]}`;
    const stepCard = document.getElementById(stepCardId);
    if (!stepCard) return;

    stepCard.classList.remove('in-progress');
    stepCard.classList.add('success');

    const messageElement = stepCard.querySelector('.step-message');
    if (messageElement) {
        if (result && result.content) {
            messageElement.textContent = result.content;
        } else {
            messageElement.textContent = '已完成';
        }
    }
}
```

### 4. 总结与收益

通过以上改造，前端将获得以下收益：
- **代码简化**: 彻底移除了复杂的轮询和相关状态管理逻辑。
- **高可靠性**: UI状态完全由服务器推送的事件驱动，避免了轮询可能带来的网络延迟和状态不一致问题。
- **松耦合**: UI的展示与数据获取逻辑分离。只要前后端约定好`stepId`，后端的业务流程调整不会破坏前端的UI展示。
- **更好的用户体验**: 用户可以接收到最及时、最准确的进度反馈。

## 七、架构演进的最终形态：告别SSEStreamAdapter

一个自然且关键的问题是：在采纳了V2.0的事件驱动架构后，原有的`stream_adapter.py`文件将何去何从？

答案是：**它的职责将被完全分解，最终该文件可以被安全地删除。** 这正是新架构的核心优势之一——**消除中间转换层，从根本上降低系统复杂度**。

### 1. 角色对比：从“状态侦探”到“彻底消失”

#### a. 旧架构下的 `SSEStreamAdapter` (状态侦探)

在旧模式下，`SSEStreamAdapter` 扮演着一个脆弱的“状态侦探”角色：
- **被动接收**: 从LangGraph获得一个巨大的、无差别的`state`字典。
- **主动猜测**: 通过检查`state`中是否存在特定字段 (`if "core_intent" in state:`) 来“猜测”业务走到了哪一步。
- **复杂翻译**: 手动将`state`中的数据翻译和格式化成前端SSE事件。

这导致`stream_adapter.py`内部充满了与业务逻辑重复的`_handle_..._events`和`_format_..._content`函数，维护成本极高。

#### b. 新架构下 (`SSEStreamAdapter` 的消亡)

新架构颠覆了这个流程：
1.  **业务节点主动宣告**: 业务节点通过`NotificationService`主动发布标准化的事件。
2.  **API层简单转发**: API层作为“邮差”，不关心事件内容，仅负责从Redis订阅并原样转发给前端。

这样一来，**不再需要任何中间层来做“猜测”和“翻译”**。`SSEStreamAdapter`的所有职责都被更合适的组件（业务节点自身和API层）所吸收。

### 2. 代码演进的直观对比

#### 精简前：依赖`SSEStreamAdapter`
```python
# src/agents/travel_planner_agent_langgraph.py (旧)

# 1. 必须导入并实例化适配器
from .stream_adapter import SSEStreamAdapter 
adapter = SSEStreamAdapter()

# 2. 需要一个循环来消费LangGraph流，并手动调用适配器
async def stream_plan(self, ...):
    langgraph_stream = self.graph.stream(...)
    # 将整个流交给适配器处理
    return adapter.convert_langgraph_stream_to_sse(langgraph_stream, ...)
```

#### 精简后：彻底移除`SSEStreamAdapter`

```python
# src/agents/travel_planner_agent_langgraph.py (新)

# 1. 不再需要SSEStreamAdapter，引入NotificationService
from src.services.notification_service import NotificationService

async def stream_plan_v2(self, request_data, background_tasks):
    # ...
    # 1. 初始化通知服务
    notification_service = NotificationService(redis_client)

    # 2. 将Agent执行放入后台，并注入通知服务
    background_tasks.add_task(
        self.graph.stream_run_automatic,
        input={...},
        config={"configurable": {"notification_service": notification_service}}
    )
    
    # 3. 立即返回一个直接监听Redis的EventSourceResponse
    return EventSourceResponse(...)
```

### 结论

实施V2.0架构后，`stream_adapter.py`文件将变得完全多余。删除它将带来以下好处：
- **降低代码量**: 移除一个近2000行的复杂文件。
- **降低维护成本**: 业务逻辑变更不再需要同步修改这个适配器。
- **提升系统健壮性**: 消除了因“猜测”逻辑不准确而导致的bug。
- **架构更清晰**: 系统的职责边界更加明确，符合单一职责原则。
